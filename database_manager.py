"""
Database Manager for Yahoo Finance Stock Data
Handles storage and indexing of all stock data in SQLite database
"""

import sqlite3
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
import pandas as pd
from pathlib import Path

from config import *


class DatabaseManager:
    """
    Manages SQLite database for storing and indexing stock data
    """
    
    def __init__(self, db_path: str = DATABASE_PATH):
        """
        Initialize database manager
        
        Args:
            db_path: Path to SQLite database file
        """
        self.db_path = db_path
        self.logger = self._setup_logging()
        self._create_database()
    
    def _setup_logging(self) -> logging.Logger:
        """Set up logging configuration"""
        logger = logging.getLogger(__name__)
        logger.setLevel(getattr(logging, LOG_LEVEL))
        
        if not logger.handlers:
            handler = logging.FileHandler(LOG_FILE)
            formatter = logging.Formatter(LOG_FORMAT)
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _create_database(self):
        """Create database tables if they don't exist"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Create stocks table (master list of all stocks)
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS stocks (
                        symbol TEXT PRIMARY KEY,
                        company_name TEXT,
                        sector TEXT,
                        industry TEXT,
                        market_cap REAL,
                        first_fetch_date TEXT,
                        last_update_date TEXT,
                        is_active BOOLEAN DEFAULT 1,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Create stock_info table (basic company information)
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS stock_info (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        symbol TEXT,
                        fetch_timestamp TEXT,
                        info_json TEXT,
                        FOREIGN KEY (symbol) REFERENCES stocks (symbol),
                        UNIQUE(symbol)
                    )
                ''')
                
                # Create price_history table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS price_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        symbol TEXT,
                        date TEXT,
                        open_price REAL,
                        high_price REAL,
                        low_price REAL,
                        close_price REAL,
                        volume INTEGER,
                        dividends REAL,
                        stock_splits REAL,
                        fetch_timestamp TEXT,
                        FOREIGN KEY (symbol) REFERENCES stocks (symbol),
                        UNIQUE(symbol, date)
                    )
                ''')
                
                # Create financials table (income statements)
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS financials (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        symbol TEXT,
                        report_date TEXT,
                        report_type TEXT, -- 'annual' or 'quarterly'
                        financial_data_json TEXT,
                        fetch_timestamp TEXT,
                        FOREIGN KEY (symbol) REFERENCES stocks (symbol),
                        UNIQUE(symbol, report_date, report_type)
                    )
                ''')
                
                # Create balance_sheet table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS balance_sheet (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        symbol TEXT,
                        report_date TEXT,
                        report_type TEXT, -- 'annual' or 'quarterly'
                        balance_sheet_json TEXT,
                        fetch_timestamp TEXT,
                        FOREIGN KEY (symbol) REFERENCES stocks (symbol),
                        UNIQUE(symbol, report_date, report_type)
                    )
                ''')
                
                # Create cashflow table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS cashflow (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        symbol TEXT,
                        report_date TEXT,
                        report_type TEXT, -- 'annual' or 'quarterly'
                        cashflow_json TEXT,
                        fetch_timestamp TEXT,
                        FOREIGN KEY (symbol) REFERENCES stocks (symbol),
                        UNIQUE(symbol, report_date, report_type)
                    )
                ''')
                
                # Create earnings table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS earnings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        symbol TEXT,
                        report_date TEXT,
                        report_type TEXT, -- 'annual', 'quarterly', or 'calendar'
                        revenue REAL,
                        earnings REAL,
                        earnings_json TEXT,
                        fetch_timestamp TEXT,
                        FOREIGN KEY (symbol) REFERENCES stocks (symbol),
                        UNIQUE(symbol, report_date, report_type)
                    )
                ''')
                
                # Create recommendations table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS recommendations (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        symbol TEXT,
                        recommendation_date TEXT,
                        recommendation_json TEXT,
                        fetch_timestamp TEXT,
                        FOREIGN KEY (symbol) REFERENCES stocks (symbol),
                        UNIQUE(symbol, recommendation_date)
                    )
                ''')
                
                # Create holders table (institutional, mutual fund, major holders)
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS holders (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        symbol TEXT,
                        holder_type TEXT, -- 'major', 'institutional', 'mutualfund'
                        holder_data_json TEXT,
                        fetch_timestamp TEXT,
                        FOREIGN KEY (symbol) REFERENCES stocks (symbol),
                        UNIQUE(symbol, holder_type)
                    )
                ''')
                
                # Create dividends table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS dividends (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        symbol TEXT,
                        ex_date TEXT,
                        dividend_amount REAL,
                        fetch_timestamp TEXT,
                        FOREIGN KEY (symbol) REFERENCES stocks (symbol),
                        UNIQUE(symbol, ex_date)
                    )
                ''')
                
                # Create splits table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS splits (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        symbol TEXT,
                        split_date TEXT,
                        split_ratio REAL,
                        fetch_timestamp TEXT,
                        FOREIGN KEY (symbol) REFERENCES stocks (symbol),
                        UNIQUE(symbol, split_date)
                    )
                ''')
                
                # Create fetch_log table (track all fetch operations)
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS fetch_log (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        symbol TEXT,
                        fetch_type TEXT,
                        fetch_timestamp TEXT,
                        success BOOLEAN,
                        error_message TEXT,
                        data_types_fetched TEXT,
                        FOREIGN KEY (symbol) REFERENCES stocks (symbol)
                    )
                ''')
                
                # Create indexes for better performance
                self._create_indexes(cursor)
                
                conn.commit()
                self.logger.info("Database tables created successfully")
                
        except Exception as e:
            self.logger.error(f"Error creating database: {str(e)}")
            raise
    
    def _create_indexes(self, cursor):
        """Create database indexes for better query performance"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_stocks_symbol ON stocks (symbol)",
            "CREATE INDEX IF NOT EXISTS idx_stock_info_symbol ON stock_info (symbol)",
            "CREATE INDEX IF NOT EXISTS idx_stock_info_timestamp ON stock_info (fetch_timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_price_history_symbol ON price_history (symbol)",
            "CREATE INDEX IF NOT EXISTS idx_price_history_date ON price_history (date)",
            "CREATE INDEX IF NOT EXISTS idx_price_history_symbol_date ON price_history (symbol, date)",
            "CREATE INDEX IF NOT EXISTS idx_financials_symbol ON financials (symbol)",
            "CREATE INDEX IF NOT EXISTS idx_financials_date ON financials (report_date)",
            "CREATE INDEX IF NOT EXISTS idx_financials_type ON financials (report_type)",
            "CREATE INDEX IF NOT EXISTS idx_balance_sheet_symbol ON balance_sheet (symbol)",
            "CREATE INDEX IF NOT EXISTS idx_balance_sheet_date ON balance_sheet (report_date)",
            "CREATE INDEX IF NOT EXISTS idx_cashflow_symbol ON cashflow (symbol)",
            "CREATE INDEX IF NOT EXISTS idx_cashflow_date ON cashflow (report_date)",
            "CREATE INDEX IF NOT EXISTS idx_earnings_symbol ON earnings (symbol)",
            "CREATE INDEX IF NOT EXISTS idx_earnings_date ON earnings (report_date)",
            "CREATE INDEX IF NOT EXISTS idx_recommendations_symbol ON recommendations (symbol)",
            "CREATE INDEX IF NOT EXISTS idx_holders_symbol ON holders (symbol)",
            "CREATE INDEX IF NOT EXISTS idx_holders_type ON holders (holder_type)",
            "CREATE INDEX IF NOT EXISTS idx_dividends_symbol ON dividends (symbol)",
            "CREATE INDEX IF NOT EXISTS idx_dividends_date ON dividends (ex_date)",
            "CREATE INDEX IF NOT EXISTS idx_splits_symbol ON splits (symbol)",
            "CREATE INDEX IF NOT EXISTS idx_fetch_log_symbol ON fetch_log (symbol)",
            "CREATE INDEX IF NOT EXISTS idx_fetch_log_timestamp ON fetch_log (fetch_timestamp)"
        ]
        
        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
            except Exception as e:
                self.logger.warning(f"Error creating index: {str(e)}")

    def store_stock_data(self, stock_data: Dict[str, Any]) -> bool:
        """
        Store comprehensive stock data in the database

        Args:
            stock_data: Dictionary containing all stock data from YahooFinanceFetcher

        Returns:
            True if successful, False otherwise
        """
        try:
            symbol = stock_data.get('symbol')
            if not symbol:
                self.logger.error("No symbol found in stock data")
                return False

            fetch_timestamp = stock_data.get('fetch_timestamp', datetime.now().isoformat())

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Store or update stock basic info
                self._store_stock_basic_info(cursor, symbol, stock_data.get('info', {}), fetch_timestamp)

                # Store stock info JSON
                if 'info' in stock_data:
                    self._store_stock_info_json(cursor, symbol, stock_data['info'], fetch_timestamp)

                # Store price history
                if 'price_history' in stock_data:
                    self._store_price_history(cursor, symbol, stock_data['price_history'], fetch_timestamp)

                # Store financials
                if 'financials' in stock_data:
                    self._store_financials(cursor, symbol, stock_data['financials'], fetch_timestamp)

                # Store balance sheet
                if 'balance_sheet' in stock_data:
                    self._store_balance_sheet(cursor, symbol, stock_data['balance_sheet'], fetch_timestamp)

                # Store cash flow
                if 'cashflow' in stock_data:
                    self._store_cashflow(cursor, symbol, stock_data['cashflow'], fetch_timestamp)

                # Store earnings
                if 'earnings' in stock_data:
                    self._store_earnings(cursor, symbol, stock_data['earnings'], fetch_timestamp)

                # Store additional data
                if 'additional_data' in stock_data:
                    self._store_additional_data(cursor, symbol, stock_data['additional_data'], fetch_timestamp)

                # Log the fetch operation
                self._log_fetch_operation(cursor, symbol, stock_data, fetch_timestamp)

                conn.commit()
                self.logger.info(f"Successfully stored data for {symbol}")
                return True

        except Exception as e:
            self.logger.error(f"Error storing stock data for {symbol}: {str(e)}")
            return False

    def _store_stock_basic_info(self, cursor, symbol: str, info: Dict[str, Any], fetch_timestamp: str):
        """Store basic stock information in stocks table"""
        try:
            company_name = info.get('longName', info.get('shortName', ''))
            sector = info.get('sector', '')
            industry = info.get('industry', '')
            market_cap = info.get('marketCap')

            # Insert or update stock basic info
            cursor.execute('''
                INSERT OR REPLACE INTO stocks
                (symbol, company_name, sector, industry, market_cap, last_update_date, first_fetch_date)
                VALUES (?, ?, ?, ?, ?, ?,
                    COALESCE((SELECT first_fetch_date FROM stocks WHERE symbol = ?), ?))
            ''', (symbol, company_name, sector, industry, market_cap, fetch_timestamp, symbol, fetch_timestamp))

        except Exception as e:
            self.logger.warning(f"Error storing basic info for {symbol}: {str(e)}")

    def _store_stock_info_json(self, cursor, symbol: str, info: Dict[str, Any], fetch_timestamp: str):
        """Store complete stock info as JSON"""
        try:
            cursor.execute('''
                INSERT OR REPLACE INTO stock_info (symbol, fetch_timestamp, info_json)
                VALUES (?, ?, ?)
            ''', (symbol, fetch_timestamp, json.dumps(info)))

        except Exception as e:
            self.logger.warning(f"Error storing info JSON for {symbol}: {str(e)}")

    def _store_price_history(self, cursor, symbol: str, price_history: List[Dict], fetch_timestamp: str):
        """Store price history data"""
        try:
            for record in price_history:
                cursor.execute('''
                    INSERT OR REPLACE INTO price_history
                    (symbol, date, open_price, high_price, low_price, close_price, volume,
                     dividends, stock_splits, fetch_timestamp)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    symbol,
                    record.get('date'),
                    record.get('open'),
                    record.get('high'),
                    record.get('low'),
                    record.get('close'),
                    record.get('volume'),
                    record.get('dividends'),
                    record.get('stock_splits'),
                    fetch_timestamp
                ))

        except Exception as e:
            self.logger.warning(f"Error storing price history for {symbol}: {str(e)}")

    def _store_financials(self, cursor, symbol: str, financials: Dict[str, List], fetch_timestamp: str):
        """Store financial statements"""
        try:
            # Store annual financials
            for record in financials.get('annual', []):
                cursor.execute('''
                    INSERT OR REPLACE INTO financials
                    (symbol, report_date, report_type, financial_data_json, fetch_timestamp)
                    VALUES (?, ?, ?, ?, ?)
                ''', (symbol, record.get('date'), 'annual', json.dumps(record.get('data', {})), fetch_timestamp))

            # Store quarterly financials
            for record in financials.get('quarterly', []):
                cursor.execute('''
                    INSERT OR REPLACE INTO financials
                    (symbol, report_date, report_type, financial_data_json, fetch_timestamp)
                    VALUES (?, ?, ?, ?, ?)
                ''', (symbol, record.get('date'), 'quarterly', json.dumps(record.get('data', {})), fetch_timestamp))

        except Exception as e:
            self.logger.warning(f"Error storing financials for {symbol}: {str(e)}")

    def _store_balance_sheet(self, cursor, symbol: str, balance_sheet: Dict[str, List], fetch_timestamp: str):
        """Store balance sheet data"""
        try:
            # Store annual balance sheet
            for record in balance_sheet.get('annual', []):
                cursor.execute('''
                    INSERT OR REPLACE INTO balance_sheet
                    (symbol, report_date, report_type, balance_sheet_json, fetch_timestamp)
                    VALUES (?, ?, ?, ?, ?)
                ''', (symbol, record.get('date'), 'annual', json.dumps(record.get('data', {})), fetch_timestamp))

            # Store quarterly balance sheet
            for record in balance_sheet.get('quarterly', []):
                cursor.execute('''
                    INSERT OR REPLACE INTO balance_sheet
                    (symbol, report_date, report_type, balance_sheet_json, fetch_timestamp)
                    VALUES (?, ?, ?, ?, ?)
                ''', (symbol, record.get('date'), 'quarterly', json.dumps(record.get('data', {})), fetch_timestamp))

        except Exception as e:
            self.logger.warning(f"Error storing balance sheet for {symbol}: {str(e)}")

    def _store_cashflow(self, cursor, symbol: str, cashflow: Dict[str, List], fetch_timestamp: str):
        """Store cash flow data"""
        try:
            # Store annual cash flow
            for record in cashflow.get('annual', []):
                cursor.execute('''
                    INSERT OR REPLACE INTO cashflow
                    (symbol, report_date, report_type, cashflow_json, fetch_timestamp)
                    VALUES (?, ?, ?, ?, ?)
                ''', (symbol, record.get('date'), 'annual', json.dumps(record.get('data', {})), fetch_timestamp))

            # Store quarterly cash flow
            for record in cashflow.get('quarterly', []):
                cursor.execute('''
                    INSERT OR REPLACE INTO cashflow
                    (symbol, report_date, report_type, cashflow_json, fetch_timestamp)
                    VALUES (?, ?, ?, ?, ?)
                ''', (symbol, record.get('date'), 'quarterly', json.dumps(record.get('data', {})), fetch_timestamp))

        except Exception as e:
            self.logger.warning(f"Error storing cash flow for {symbol}: {str(e)}")

    def _store_earnings(self, cursor, symbol: str, earnings: Dict[str, List], fetch_timestamp: str):
        """Store earnings data including new income statement data"""
        try:
            # Store annual earnings (backward compatibility)
            for record in earnings.get('annual_earnings', []):
                cursor.execute('''
                    INSERT OR REPLACE INTO earnings
                    (symbol, report_date, report_type, revenue, earnings, earnings_json, fetch_timestamp)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    symbol,
                    record.get('year'),
                    'annual',
                    record.get('revenue'),
                    record.get('earnings'),
                    json.dumps(record),
                    fetch_timestamp
                ))

            # Store quarterly earnings (backward compatibility)
            for record in earnings.get('quarterly_earnings', []):
                cursor.execute('''
                    INSERT OR REPLACE INTO earnings
                    (symbol, report_date, report_type, revenue, earnings, earnings_json, fetch_timestamp)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    symbol,
                    record.get('quarter'),
                    'quarterly',
                    record.get('revenue'),
                    record.get('earnings'),
                    json.dumps(record),
                    fetch_timestamp
                ))

            # Store annual income statements (new detailed data)
            for record in earnings.get('income_stmt', []):
                cursor.execute('''
                    INSERT OR REPLACE INTO earnings
                    (symbol, report_date, report_type, earnings_json, fetch_timestamp)
                    VALUES (?, ?, ?, ?, ?)
                ''', (symbol, record.get('date'), 'annual_income_stmt', json.dumps(record), fetch_timestamp))

            # Store quarterly income statements (new detailed data)
            for record in earnings.get('quarterly_income_stmt', []):
                cursor.execute('''
                    INSERT OR REPLACE INTO earnings
                    (symbol, report_date, report_type, earnings_json, fetch_timestamp)
                    VALUES (?, ?, ?, ?, ?)
                ''', (symbol, record.get('date'), 'quarterly_income_stmt', json.dumps(record), fetch_timestamp))

            # Store earnings calendar
            for record in earnings.get('earnings_calendar', []):
                cursor.execute('''
                    INSERT OR REPLACE INTO earnings
                    (symbol, report_date, report_type, earnings_json, fetch_timestamp)
                    VALUES (?, ?, ?, ?, ?)
                ''', (symbol, record.get('date'), 'calendar', json.dumps(record), fetch_timestamp))

        except Exception as e:
            self.logger.warning(f"Error storing earnings for {symbol}: {str(e)}")

    def _store_additional_data(self, cursor, symbol: str, additional_data: Dict[str, List], fetch_timestamp: str):
        """Store additional data (recommendations, holders, dividends, splits)"""
        try:
            # Store recommendations
            for record in additional_data.get('recommendations', []):
                cursor.execute('''
                    INSERT OR REPLACE INTO recommendations
                    (symbol, recommendation_date, recommendation_json, fetch_timestamp)
                    VALUES (?, ?, ?, ?)
                ''', (symbol, record.get('date'), json.dumps(record), fetch_timestamp))

            # Store holders data
            for holder_type in ['major_holders', 'institutional_holders', 'mutualfund_holders']:
                if holder_type in additional_data:
                    cursor.execute('''
                        INSERT OR REPLACE INTO holders
                        (symbol, holder_type, holder_data_json, fetch_timestamp)
                        VALUES (?, ?, ?, ?)
                    ''', (symbol, holder_type.replace('_holders', ''),
                         json.dumps(additional_data[holder_type]), fetch_timestamp))

            # Store dividends
            for record in additional_data.get('dividends', []):
                cursor.execute('''
                    INSERT OR REPLACE INTO dividends
                    (symbol, ex_date, dividend_amount, fetch_timestamp)
                    VALUES (?, ?, ?, ?)
                ''', (symbol, record.get('date'), record.get('dividend'), fetch_timestamp))

            # Store splits
            for record in additional_data.get('splits', []):
                cursor.execute('''
                    INSERT OR REPLACE INTO splits
                    (symbol, split_date, split_ratio, fetch_timestamp)
                    VALUES (?, ?, ?, ?)
                ''', (symbol, record.get('date'), record.get('split_ratio'), fetch_timestamp))

        except Exception as e:
            self.logger.warning(f"Error storing additional data for {symbol}: {str(e)}")

    def _log_fetch_operation(self, cursor, symbol: str, stock_data: Dict[str, Any], fetch_timestamp: str):
        """Log the fetch operation"""
        try:
            success = len(stock_data.get('errors', [])) == 0
            error_message = '; '.join(stock_data.get('errors', [])) if stock_data.get('errors') else None
            data_types = ', '.join(stock_data.get('data_types_fetched', []))

            cursor.execute('''
                INSERT INTO fetch_log
                (symbol, fetch_type, fetch_timestamp, success, error_message, data_types_fetched)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (symbol, 'comprehensive', fetch_timestamp, success, error_message, data_types))

        except Exception as e:
            self.logger.warning(f"Error logging fetch operation for {symbol}: {str(e)}")

    def get_stock_info(self, symbol: str, latest: bool = True) -> Optional[Dict[str, Any]]:
        """
        Retrieve stock information from database

        Args:
            symbol: Stock symbol
            latest: If True, get latest data; if False, get all historical data

        Returns:
            Stock information dictionary or None if not found
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                if latest:
                    cursor.execute('''
                        SELECT info_json, fetch_timestamp FROM stock_info
                        WHERE symbol = ? ORDER BY fetch_timestamp DESC LIMIT 1
                    ''', (symbol,))

                    result = cursor.fetchone()
                    if result:
                        return {
                            'symbol': symbol,
                            'info': json.loads(result[0]),
                            'fetch_timestamp': result[1]
                        }
                else:
                    cursor.execute('''
                        SELECT info_json, fetch_timestamp FROM stock_info
                        WHERE symbol = ? ORDER BY fetch_timestamp DESC
                    ''', (symbol,))

                    results = cursor.fetchall()
                    return [{
                        'symbol': symbol,
                        'info': json.loads(row[0]),
                        'fetch_timestamp': row[1]
                    } for row in results]

                return None

        except Exception as e:
            self.logger.error(f"Error retrieving stock info for {symbol}: {str(e)}")
            return None

    def get_price_history(self, symbol: str, start_date: Optional[str] = None,
                         end_date: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Retrieve price history from database

        Args:
            symbol: Stock symbol
            start_date: Start date (YYYY-MM-DD format)
            end_date: End date (YYYY-MM-DD format)

        Returns:
            List of price history records
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                query = '''
                    SELECT date, open_price, high_price, low_price, close_price, volume,
                           dividends, stock_splits, fetch_timestamp
                    FROM price_history WHERE symbol = ?
                '''
                params = [symbol]

                if start_date:
                    query += ' AND date >= ?'
                    params.append(start_date)

                if end_date:
                    query += ' AND date <= ?'
                    params.append(end_date)

                query += ' ORDER BY date'

                cursor.execute(query, params)
                results = cursor.fetchall()

                return [{
                    'date': row[0],
                    'open': row[1],
                    'high': row[2],
                    'low': row[3],
                    'close': row[4],
                    'volume': row[5],
                    'dividends': row[6],
                    'stock_splits': row[7],
                    'fetch_timestamp': row[8]
                } for row in results]

        except Exception as e:
            self.logger.error(f"Error retrieving price history for {symbol}: {str(e)}")
            return []

    def get_financials(self, symbol: str, report_type: str = 'both') -> Dict[str, List]:
        """
        Retrieve financial statements from database

        Args:
            symbol: Stock symbol
            report_type: 'annual', 'quarterly', or 'both'

        Returns:
            Dictionary containing financial data
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                result = {'annual': [], 'quarterly': []}

                if report_type in ['annual', 'both']:
                    cursor.execute('''
                        SELECT report_date, financial_data_json, fetch_timestamp
                        FROM financials WHERE symbol = ? AND report_type = 'annual'
                        ORDER BY report_date DESC
                    ''', (symbol,))

                    for row in cursor.fetchall():
                        result['annual'].append({
                            'date': row[0],
                            'data': json.loads(row[1]),
                            'fetch_timestamp': row[2]
                        })

                if report_type in ['quarterly', 'both']:
                    cursor.execute('''
                        SELECT report_date, financial_data_json, fetch_timestamp
                        FROM financials WHERE symbol = ? AND report_type = 'quarterly'
                        ORDER BY report_date DESC
                    ''', (symbol,))

                    for row in cursor.fetchall():
                        result['quarterly'].append({
                            'date': row[0],
                            'data': json.loads(row[1]),
                            'fetch_timestamp': row[2]
                        })

                return result

        except Exception as e:
            self.logger.error(f"Error retrieving financials for {symbol}: {str(e)}")
            return {'annual': [], 'quarterly': []}

    def get_earnings(self, symbol: str) -> Dict[str, List]:
        """
        Retrieve earnings data from database

        Args:
            symbol: Stock symbol

        Returns:
            Dictionary containing earnings data
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                result = {'annual': [], 'quarterly': [], 'calendar': []}

                # Get annual earnings
                cursor.execute('''
                    SELECT report_date, revenue, earnings, earnings_json, fetch_timestamp
                    FROM earnings WHERE symbol = ? AND report_type = 'annual'
                    ORDER BY report_date DESC
                ''', (symbol,))

                for row in cursor.fetchall():
                    result['annual'].append({
                        'year': row[0],
                        'revenue': row[1],
                        'earnings': row[2],
                        'data': json.loads(row[3]),
                        'fetch_timestamp': row[4]
                    })

                # Get quarterly earnings
                cursor.execute('''
                    SELECT report_date, revenue, earnings, earnings_json, fetch_timestamp
                    FROM earnings WHERE symbol = ? AND report_type = 'quarterly'
                    ORDER BY report_date DESC
                ''', (symbol,))

                for row in cursor.fetchall():
                    result['quarterly'].append({
                        'quarter': row[0],
                        'revenue': row[1],
                        'earnings': row[2],
                        'data': json.loads(row[3]),
                        'fetch_timestamp': row[4]
                    })

                # Get earnings calendar
                cursor.execute('''
                    SELECT report_date, earnings_json, fetch_timestamp
                    FROM earnings WHERE symbol = ? AND report_type = 'calendar'
                    ORDER BY report_date DESC
                ''', (symbol,))

                for row in cursor.fetchall():
                    result['calendar'].append({
                        'date': row[0],
                        'data': json.loads(row[1]),
                        'fetch_timestamp': row[2]
                    })

                return result

        except Exception as e:
            self.logger.error(f"Error retrieving earnings for {symbol}: {str(e)}")
            return {'annual': [], 'quarterly': [], 'calendar': []}

    def search_stocks(self, query: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Search for stocks by symbol, company name, sector, or industry

        Args:
            query: Search query
            limit: Maximum number of results

        Returns:
            List of matching stocks
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                search_query = f"%{query}%"
                cursor.execute('''
                    SELECT symbol, company_name, sector, industry, market_cap, last_update_date
                    FROM stocks
                    WHERE symbol LIKE ? OR company_name LIKE ? OR sector LIKE ? OR industry LIKE ?
                    ORDER BY market_cap DESC
                    LIMIT ?
                ''', (search_query, search_query, search_query, search_query, limit))

                results = cursor.fetchall()
                return [{
                    'symbol': row[0],
                    'company_name': row[1],
                    'sector': row[2],
                    'industry': row[3],
                    'market_cap': row[4],
                    'last_update_date': row[5]
                } for row in results]

        except Exception as e:
            self.logger.error(f"Error searching stocks with query '{query}': {str(e)}")
            return []

    def get_database_stats(self) -> Dict[str, Any]:
        """
        Get database statistics

        Returns:
            Dictionary containing database statistics
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                stats = {}

                # Count records in each table
                tables = ['stocks', 'stock_info', 'price_history', 'financials',
                         'balance_sheet', 'cashflow', 'earnings', 'recommendations',
                         'holders', 'dividends', 'splits', 'fetch_log']

                for table in tables:
                    cursor.execute(f'SELECT COUNT(*) FROM {table}')
                    stats[f'{table}_count'] = cursor.fetchone()[0]

                # Get date range of data
                cursor.execute('SELECT MIN(date), MAX(date) FROM price_history')
                date_range = cursor.fetchone()
                stats['price_data_date_range'] = {
                    'start': date_range[0],
                    'end': date_range[1]
                }

                # Get most recent fetch timestamp
                cursor.execute('SELECT MAX(fetch_timestamp) FROM fetch_log')
                stats['last_fetch'] = cursor.fetchone()[0]

                return stats

        except Exception as e:
            self.logger.error(f"Error getting database stats: {str(e)}")
            return {}
