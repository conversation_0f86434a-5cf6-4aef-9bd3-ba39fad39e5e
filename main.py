#!/usr/bin/env python3
"""
Yahoo Finance Stock Data Fetcher - Main Interface
Comprehensive tool for fetching and storing stock data from Yahoo Finance API

Usage Examples:
    # Fetch single stock
    python main.py --symbol AAPL
    
    # Fetch multiple stocks
    python main.py --symbols AAPL,MSFT,GOOGL
    
    # Fetch S&P 500 data
    python main.py --sp500
    
    # Search stocks in database
    python main.py --search "technology"
    
    # Get database statistics
    python main.py --stats
"""

import argparse
import sys
import json
from datetime import datetime
from typing import List, Dict, Any

from stock_data_manager import StockDataManager
from utils import setup_logging, validate_stock_symbol, format_large_number
from config import *


def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(
        description='Yahoo Finance Stock Data Fetcher',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    # Single stock operations
    parser.add_argument('--symbol', '-s', type=str,
                       help='Fetch data for a single stock symbol (e.g., AAPL)')
    
    # Multiple stock operations
    parser.add_argument('--symbols', type=str,
                       help='Fetch data for multiple stocks (comma-separated, e.g., AAPL,MSFT,GOOGL)')
    
    parser.add_argument('--file', '-f', type=str,
                       help='Read stock symbols from file (one per line)')
    
    # Bulk operations
    parser.add_argument('--sp500', action='store_true',
                       help='Fetch data for all S&P 500 stocks')
    
    # Data options
    parser.add_argument('--no-history', action='store_true',
                       help='Skip price history (faster, smaller data)')
    
    parser.add_argument('--period', type=str, default=DEFAULT_PERIOD,
                       choices=['1d', '5d', '1mo', '3mo', '6mo', '1y', '2y', '5y', '10y', 'ytd', 'max'],
                       help='Period for historical data (default: %(default)s)')
    
    parser.add_argument('--interval', type=str, default=DEFAULT_INTERVAL,
                       choices=['1m', '2m', '5m', '15m', '30m', '60m', '90m', '1h', '1d', '5d', '1wk', '1mo', '3mo'],
                       help='Interval for historical data (default: %(default)s)')
    
    parser.add_argument('--force-refresh', action='store_true',
                       help='Force refresh even if data is recent')
    
    # Database operations
    parser.add_argument('--search', type=str,
                       help='Search stocks in database by symbol, name, sector, or industry')
    
    parser.add_argument('--get', type=str,
                       help='Get stored data for a specific stock symbol')
    
    parser.add_argument('--stats', action='store_true',
                       help='Show database statistics')
    
    # Output options
    parser.add_argument('--output', '-o', type=str,
                       help='Save results to JSON file')
    
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    
    parser.add_argument('--quiet', '-q', action='store_true',
                       help='Suppress console output')
    
    # Rate limiting
    parser.add_argument('--rate-limit', type=float, default=REQUESTS_PER_SECOND,
                       help='Requests per second (default: %(default)s)')
    
    parser.add_argument('--max-workers', type=int, default=MAX_CONCURRENT_REQUESTS,
                       help='Maximum concurrent requests (default: %(default)s)')
    
    args = parser.parse_args()
    
    # Set up logging
    log_level = 'DEBUG' if args.verbose else 'WARNING' if args.quiet else LOG_LEVEL
    logger = setup_logging(__name__)
    
    # Initialize stock data manager
    try:
        manager = StockDataManager(rate_limit=args.rate_limit)
        logger.info("Stock Data Manager initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize Stock Data Manager: {str(e)}")
        sys.exit(1)
    
    results = {}
    
    try:
        # Handle different operations
        if args.symbol:
            # Single stock fetch
            if not validate_stock_symbol(args.symbol):
                logger.error(f"Invalid stock symbol: {args.symbol}")
                sys.exit(1)
            
            logger.info(f"Fetching data for {args.symbol}")
            result = manager.fetch_and_store_stock(
                symbol=args.symbol.upper(),
                include_history=not args.no_history,
                period=args.period,
                interval=args.interval,
                force_refresh=args.force_refresh
            )
            results['single_stock'] = result
            print_single_stock_result(result)
        
        elif args.symbols:
            # Multiple stocks fetch
            symbols = [s.strip().upper() for s in args.symbols.split(',')]
            invalid_symbols = [s for s in symbols if not validate_stock_symbol(s)]
            
            if invalid_symbols:
                logger.error(f"Invalid stock symbols: {invalid_symbols}")
                sys.exit(1)
            
            logger.info(f"Fetching data for {len(symbols)} stocks")
            result = manager.fetch_and_store_multiple_stocks(
                symbols=symbols,
                include_history=not args.no_history,
                period=args.period,
                interval=args.interval,
                max_workers=args.max_workers,
                force_refresh=args.force_refresh
            )
            results['multiple_stocks'] = result
            print_batch_result(result)
        
        elif args.file:
            # Read symbols from file
            try:
                with open(args.file, 'r') as f:
                    symbols = [line.strip().upper() for line in f if line.strip()]
                
                invalid_symbols = [s for s in symbols if not validate_stock_symbol(s)]
                if invalid_symbols:
                    logger.warning(f"Skipping invalid symbols: {invalid_symbols}")
                    symbols = [s for s in symbols if validate_stock_symbol(s)]
                
                if not symbols:
                    logger.error("No valid symbols found in file")
                    sys.exit(1)
                
                logger.info(f"Fetching data for {len(symbols)} stocks from file")
                result = manager.fetch_and_store_multiple_stocks(
                    symbols=symbols,
                    include_history=not args.no_history,
                    period=args.period,
                    interval=args.interval,
                    max_workers=args.max_workers,
                    force_refresh=args.force_refresh
                )
                results['file_stocks'] = result
                print_batch_result(result)
                
            except FileNotFoundError:
                logger.error(f"File not found: {args.file}")
                sys.exit(1)
            except Exception as e:
                logger.error(f"Error reading file {args.file}: {str(e)}")
                sys.exit(1)
        
        elif args.sp500:
            # S&P 500 fetch
            logger.info("Fetching S&P 500 data")
            result = manager.fetch_sp500_data(
                include_history=not args.no_history,
                force_refresh=args.force_refresh
            )
            results['sp500'] = result
            print_batch_result(result)
        
        elif args.search:
            # Search stocks
            logger.info(f"Searching for: {args.search}")
            search_results = manager.search_stocks(args.search)
            results['search'] = search_results
            print_search_results(search_results, args.search)
        
        elif args.get:
            # Get stored data
            if not validate_stock_symbol(args.get):
                logger.error(f"Invalid stock symbol: {args.get}")
                sys.exit(1)
            
            logger.info(f"Retrieving stored data for {args.get}")
            stock_data = manager.get_stock_data(args.get.upper())
            results['retrieved_data'] = stock_data
            print_stored_data(stock_data, args.get.upper())
        
        elif args.stats:
            # Database statistics
            logger.info("Retrieving database statistics")
            stats = manager.get_database_stats()
            results['stats'] = stats
            print_database_stats(stats)
        
        else:
            # No operation specified
            parser.print_help()
            sys.exit(1)
        
        # Save results to file if requested
        if args.output:
            try:
                with open(args.output, 'w') as f:
                    json.dump(results, f, indent=2, default=str)
                logger.info(f"Results saved to {args.output}")
            except Exception as e:
                logger.error(f"Error saving results to {args.output}: {str(e)}")
        
        logger.info("Operation completed successfully")
    
    except KeyboardInterrupt:
        logger.info("Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        sys.exit(1)


def print_single_stock_result(result: Dict[str, Any]):
    """Print results for single stock operation"""
    symbol = result.get('symbol', 'Unknown')
    status = result.get('status', 'unknown')
    
    print(f"\n=== Results for {symbol} ===")
    print(f"Status: {status}")
    
    if status == 'success':
        data_types = result.get('data_types_fetched', [])
        print(f"Data types fetched: {', '.join(data_types)}")
        
        if result.get('errors'):
            print(f"Warnings: {'; '.join(result['errors'])}")
    
    elif status == 'skipped':
        print(f"Reason: {result.get('reason', 'Unknown')}")
    
    elif status == 'failed':
        print(f"Error: {result.get('error', 'Unknown error')}")
    
    print(f"Timestamp: {result.get('timestamp', 'Unknown')}")


def print_batch_result(result: Dict[str, Any]):
    """Print results for batch operations"""
    total = result.get('total_symbols', 0)
    successful = result.get('successful', 0)
    failed = result.get('failed', 0)
    skipped = result.get('skipped', 0)
    
    print(f"\n=== Batch Processing Results ===")
    print(f"Total symbols: {total}")
    print(f"Successful: {successful}")
    print(f"Failed: {failed}")
    print(f"Skipped: {skipped}")
    
    if total > 0:
        success_rate = (successful / total) * 100
        print(f"Success rate: {success_rate:.1f}%")
    
    # Show failed symbols if any
    if failed > 0 and 'results' in result:
        failed_symbols = [symbol for symbol, res in result['results'].items() 
                         if res.get('status') == 'failed']
        if failed_symbols:
            print(f"Failed symbols: {', '.join(failed_symbols[:10])}")
            if len(failed_symbols) > 10:
                print(f"... and {len(failed_symbols) - 10} more")
    
    print(f"Completed: {result.get('batch_timestamp', 'Unknown')}")


def print_search_results(results: List[Dict[str, Any]], query: str):
    """Print search results"""
    print(f"\n=== Search Results for '{query}' ===")
    
    if not results:
        print("No results found")
        return
    
    print(f"Found {len(results)} results:\n")
    
    for i, stock in enumerate(results[:20], 1):  # Show top 20 results
        symbol = stock.get('symbol', 'N/A')
        name = stock.get('company_name', 'N/A')
        sector = stock.get('sector', 'N/A')
        market_cap = stock.get('market_cap')
        
        print(f"{i:2d}. {symbol:8s} - {name[:40]:40s}")
        print(f"     Sector: {sector:20s} Market Cap: {format_large_number(market_cap)}")
        print()
    
    if len(results) > 20:
        print(f"... and {len(results) - 20} more results")


def print_stored_data(data: Dict[str, Any], symbol: str):
    """Print stored data summary"""
    print(f"\n=== Stored Data for {symbol} ===")
    
    if not data:
        print("No data found in database")
        return
    
    info = data.get('info', {}).get('info', {})
    if info:
        print(f"Company: {info.get('longName', 'N/A')}")
        print(f"Sector: {info.get('sector', 'N/A')}")
        print(f"Industry: {info.get('industry', 'N/A')}")
        print(f"Market Cap: {format_large_number(info.get('marketCap'))}")
    
    price_history = data.get('price_history', [])
    if price_history:
        print(f"Price history: {len(price_history)} records")
        latest = price_history[-1] if price_history else {}
        if latest:
            print(f"Latest price: ${latest.get('close', 'N/A')} ({latest.get('date', 'N/A')})")
    
    financials = data.get('financials', {})
    annual_count = len(financials.get('annual', []))
    quarterly_count = len(financials.get('quarterly', []))
    if annual_count or quarterly_count:
        print(f"Financials: {annual_count} annual, {quarterly_count} quarterly reports")
    
    earnings = data.get('earnings', {})
    earnings_annual = len(earnings.get('annual', []))
    earnings_quarterly = len(earnings.get('quarterly', []))
    if earnings_annual or earnings_quarterly:
        print(f"Earnings: {earnings_annual} annual, {earnings_quarterly} quarterly reports")
    
    print(f"Retrieved: {data.get('retrieved_at', 'Unknown')}")


def print_database_stats(stats: Dict[str, Any]):
    """Print database statistics"""
    print("\n=== Database Statistics ===")
    
    # Record counts
    print("Record counts:")
    for key, value in stats.items():
        if key.endswith('_count'):
            table_name = key.replace('_count', '').replace('_', ' ').title()
            print(f"  {table_name:20s}: {value:,}")
    
    # Date ranges
    date_range = stats.get('price_data_date_range', {})
    if date_range.get('start') and date_range.get('end'):
        print(f"\nPrice data range: {date_range['start']} to {date_range['end']}")
    
    # Last fetch
    last_fetch = stats.get('last_fetch')
    if last_fetch:
        print(f"Last fetch: {last_fetch}")


if __name__ == '__main__':
    main()
