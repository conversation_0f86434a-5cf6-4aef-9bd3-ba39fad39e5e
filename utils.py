"""
Utility functions for data validation, error handling, and logging
"""

import re
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
import pandas as pd
import numpy as np
from pathlib import Path

from config import *


def setup_logging(name: str = __name__, log_file: str = LOG_FILE) -> logging.Logger:
    """
    Set up comprehensive logging configuration
    
    Args:
        name: Logger name
        log_file: Log file path
        
    Returns:
        Configured logger
    """
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, LOG_LEVEL))
    
    # Avoid duplicate handlers
    if logger.handlers:
        return logger
    
    # Create formatters
    detailed_formatter = logging.Formatter(LOG_FORMAT)
    simple_formatter = logging.Formatter('%(levelname)s - %(message)s')
    
    # File handler for detailed logging
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(detailed_formatter)
    logger.addHandler(file_handler)
    
    # Console handler for important messages
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(simple_formatter)
    logger.addHandler(console_handler)
    
    return logger


def validate_stock_symbol(symbol: str) -> bool:
    """
    Validate stock symbol format
    
    Args:
        symbol: Stock symbol to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not symbol or not isinstance(symbol, str):
        return False
    
    # Basic validation: 1-5 characters, letters and numbers, may contain dots or dashes
    pattern = r'^[A-Z0-9.-]{1,10}$'
    return bool(re.match(pattern, symbol.upper()))


def validate_date_string(date_str: str) -> bool:
    """
    Validate date string format (YYYY-MM-DD)
    
    Args:
        date_str: Date string to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not date_str or not isinstance(date_str, str):
        return False
    
    try:
        datetime.strptime(date_str, '%Y-%m-%d')
        return True
    except ValueError:
        return False


def validate_period(period: str) -> bool:
    """
    Validate Yahoo Finance period parameter
    
    Args:
        period: Period string to validate
        
    Returns:
        True if valid, False otherwise
    """
    valid_periods = ['1d', '5d', '1mo', '3mo', '6mo', '1y', '2y', '5y', '10y', 'ytd', 'max']
    return period in valid_periods


def validate_interval(interval: str) -> bool:
    """
    Validate Yahoo Finance interval parameter
    
    Args:
        interval: Interval string to validate
        
    Returns:
        True if valid, False otherwise
    """
    valid_intervals = ['1m', '2m', '5m', '15m', '30m', '60m', '90m', '1h', '1d', '5d', '1wk', '1mo', '3mo']
    return interval in valid_intervals


def clean_numeric_value(value: Any) -> Optional[float]:
    """
    Clean and convert numeric values, handling various data types
    
    Args:
        value: Value to clean and convert
        
    Returns:
        Float value or None if conversion fails
    """
    if value is None or pd.isna(value):
        return None
    
    if isinstance(value, (int, float)):
        if np.isfinite(value):
            return float(value)
        return None
    
    if isinstance(value, str):
        # Remove common formatting characters
        cleaned = value.replace(',', '').replace('$', '').replace('%', '').strip()
        try:
            return float(cleaned)
        except ValueError:
            return None
    
    # Handle numpy types
    if isinstance(value, (np.integer, np.floating)):
        if np.isfinite(value):
            return float(value.item())
        return None
    
    return None


def clean_string_value(value: Any) -> Optional[str]:
    """
    Clean and convert string values
    
    Args:
        value: Value to clean and convert
        
    Returns:
        String value or None if conversion fails
    """
    if value is None or pd.isna(value):
        return None
    
    if isinstance(value, str):
        cleaned = value.strip()
        return cleaned if cleaned else None
    
    # Convert other types to string
    try:
        return str(value)
    except:
        return None


def validate_stock_data(stock_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate and clean stock data dictionary
    
    Args:
        stock_data: Raw stock data dictionary
        
    Returns:
        Dictionary with validation results and cleaned data
    """
    validation_result = {
        'is_valid': True,
        'errors': [],
        'warnings': [],
        'cleaned_data': {}
    }
    
    # Validate required fields
    if 'symbol' not in stock_data:
        validation_result['errors'].append('Missing required field: symbol')
        validation_result['is_valid'] = False
        return validation_result
    
    symbol = stock_data['symbol']
    if not validate_stock_symbol(symbol):
        validation_result['errors'].append(f'Invalid stock symbol: {symbol}')
        validation_result['is_valid'] = False
    
    # Clean and validate each data section
    cleaned_data = {'symbol': symbol.upper()}
    
    # Validate info section
    if 'info' in stock_data:
        cleaned_info = {}
        for key, value in stock_data['info'].items():
            if key in ['marketCap', 'enterpriseValue', 'totalRevenue', 'totalCash']:
                cleaned_info[key] = clean_numeric_value(value)
            elif key in ['longName', 'shortName', 'sector', 'industry']:
                cleaned_info[key] = clean_string_value(value)
            else:
                cleaned_info[key] = value
        cleaned_data['info'] = cleaned_info
    
    # Validate price history
    if 'price_history' in stock_data:
        cleaned_history = []
        for record in stock_data['price_history']:
            if isinstance(record, dict):
                cleaned_record = {}
                for field in ['open', 'high', 'low', 'close', 'volume']:
                    cleaned_record[field] = clean_numeric_value(record.get(field))
                
                if 'date' in record:
                    cleaned_record['date'] = record['date']
                
                cleaned_history.append(cleaned_record)
        cleaned_data['price_history'] = cleaned_history
    
    # Add timestamp
    cleaned_data['validation_timestamp'] = datetime.now().isoformat()
    validation_result['cleaned_data'] = cleaned_data
    
    return validation_result


def handle_api_error(error: Exception, symbol: str, operation: str) -> Dict[str, Any]:
    """
    Handle and categorize API errors
    
    Args:
        error: Exception that occurred
        symbol: Stock symbol being processed
        operation: Operation that failed
        
    Returns:
        Dictionary with error information
    """
    error_info = {
        'symbol': symbol,
        'operation': operation,
        'error_type': type(error).__name__,
        'error_message': str(error),
        'timestamp': datetime.now().isoformat(),
        'is_retryable': False,
        'suggested_action': 'log_and_continue'
    }
    
    error_message = str(error).lower()
    
    # Categorize common errors
    if 'timeout' in error_message or 'timed out' in error_message:
        error_info['is_retryable'] = True
        error_info['suggested_action'] = 'retry_with_delay'
    elif 'connection' in error_message or 'network' in error_message:
        error_info['is_retryable'] = True
        error_info['suggested_action'] = 'retry_with_delay'
    elif 'rate limit' in error_message or '429' in error_message:
        error_info['is_retryable'] = True
        error_info['suggested_action'] = 'retry_with_longer_delay'
    elif 'not found' in error_message or '404' in error_message:
        error_info['suggested_action'] = 'skip_symbol'
    elif 'unauthorized' in error_message or '401' in error_message:
        error_info['suggested_action'] = 'check_credentials'
    
    return error_info


def create_backup(source_path: str, backup_dir: str = 'backups') -> bool:
    """
    Create backup of database or important files
    
    Args:
        source_path: Path to file to backup
        backup_dir: Directory to store backups
        
    Returns:
        True if backup successful, False otherwise
    """
    try:
        source = Path(source_path)
        if not source.exists():
            return False
        
        backup_path = Path(backup_dir)
        backup_path.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_file = backup_path / f"{source.stem}_{timestamp}{source.suffix}"
        
        import shutil
        shutil.copy2(source, backup_file)
        
        return True
        
    except Exception as e:
        logger = setup_logging()
        logger.error(f"Error creating backup: {str(e)}")
        return False


def format_large_number(number: Optional[float]) -> str:
    """
    Format large numbers for display (e.g., 1.5B, 250M)
    
    Args:
        number: Number to format
        
    Returns:
        Formatted string
    """
    if number is None or not isinstance(number, (int, float)):
        return 'N/A'
    
    if abs(number) >= 1e12:
        return f"{number/1e12:.1f}T"
    elif abs(number) >= 1e9:
        return f"{number/1e9:.1f}B"
    elif abs(number) >= 1e6:
        return f"{number/1e6:.1f}M"
    elif abs(number) >= 1e3:
        return f"{number/1e3:.1f}K"
    else:
        return f"{number:.2f}"


def calculate_data_freshness(timestamp_str: str) -> Dict[str, Any]:
    """
    Calculate how fresh the data is
    
    Args:
        timestamp_str: ISO format timestamp string
        
    Returns:
        Dictionary with freshness information
    """
    try:
        timestamp = datetime.fromisoformat(timestamp_str)
        now = datetime.now()
        delta = now - timestamp
        
        hours = delta.total_seconds() / 3600
        
        if hours < 1:
            freshness = 'very_fresh'
            description = f"{int(delta.total_seconds() / 60)} minutes ago"
        elif hours < 24:
            freshness = 'fresh'
            description = f"{int(hours)} hours ago"
        elif hours < 168:  # 1 week
            freshness = 'moderate'
            description = f"{int(hours / 24)} days ago"
        else:
            freshness = 'stale'
            description = f"{int(hours / 168)} weeks ago"
        
        return {
            'freshness': freshness,
            'description': description,
            'hours_old': hours,
            'needs_refresh': hours >= DATA_REFRESH_HOURS
        }
        
    except Exception:
        return {
            'freshness': 'unknown',
            'description': 'Unknown age',
            'hours_old': float('inf'),
            'needs_refresh': True
        }
