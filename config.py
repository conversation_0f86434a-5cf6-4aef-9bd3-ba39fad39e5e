"""
Configuration settings for Yahoo Finance Stock Data Fetcher
"""

import os
from datetime import datetime, timedelta

# Database configuration
DATABASE_PATH = "stock_data.db"
BACKUP_DATABASE_PATH = "stock_data_backup.db"

# Yahoo Finance API settings
DEFAULT_PERIOD = "5y"  # Default period for historical data
DEFAULT_INTERVAL = "1d"  # Default interval for historical data
MAX_RETRIES = 3
RETRY_DELAY = 1  # seconds
REQUEST_TIMEOUT = 30  # seconds

# Rate limiting settings
REQUESTS_PER_SECOND = 2  # Conservative rate limiting
BATCH_SIZE = 50  # Number of stocks to process in each batch
MAX_CONCURRENT_REQUESTS = 5

# Data freshness settings
DATA_REFRESH_HOURS = 24  # Hours before data is considered stale
EARNINGS_REFRESH_DAYS = 7  # Days before earnings data is refreshed

# Logging configuration
LOG_LEVEL = "INFO"
LOG_FILE = "yahoo_finance_fetcher.log"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Stock data types to fetch
STOCK_DATA_TYPES = [
    "info",
    "history",
    "financials",
    "quarterly_financials", 
    "balance_sheet",
    "quarterly_balance_sheet",
    "cashflow",
    "quarterly_cashflow",
    "earnings",
    "quarterly_earnings",
    "recommendations",
    "calendar",
    "major_holders",
    "institutional_holders",
    "mutualfund_holders",
    "dividends",
    "splits",
    "actions"
]

# Database table names
TABLES = {
    "stocks": "stocks",
    "stock_info": "stock_info", 
    "price_history": "price_history",
    "financials": "financials",
    "balance_sheet": "balance_sheet",
    "cashflow": "cashflow",
    "earnings": "earnings",
    "recommendations": "recommendations",
    "holders": "holders",
    "dividends": "dividends",
    "splits": "splits",
    "fetch_log": "fetch_log"
}
