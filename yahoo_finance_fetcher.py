"""
Yahoo Finance Data Fetcher
Comprehensive class to fetch all available stock data from Yahoo Finance API
"""

import warnings
# Suppress yfinance deprecation warnings
warnings.filterwarnings("ignore", category=DeprecationWarning, module="yfinance")

import yfinance as yf
import pandas as pd
import numpy as np
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union, Any
import json
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm

from config import *


class YahooFinanceFetcher:
    """
    Comprehensive Yahoo Finance data fetcher that can handle single stocks or bulk operations
    """
    
    def __init__(self, rate_limit: float = REQUESTS_PER_SECOND):
        """
        Initialize the Yahoo Finance fetcher
        
        Args:
            rate_limit: Maximum requests per second (default from config)
        """
        self.rate_limit = rate_limit
        self.last_request_time = 0
        self.logger = self._setup_logging()
        
    def _setup_logging(self) -> logging.Logger:
        """Set up logging configuration"""
        logger = logging.getLogger(__name__)
        logger.setLevel(getattr(logging, LOG_LEVEL))
        
        if not logger.handlers:
            handler = logging.FileHandler(LOG_FILE)
            formatter = logging.Formatter(LOG_FORMAT)
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
            # Also log to console
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
            
        return logger
    
    def _rate_limit_wait(self):
        """Implement rate limiting"""
        if self.rate_limit > 0:
            time_since_last = time.time() - self.last_request_time
            min_interval = 1.0 / self.rate_limit
            if time_since_last < min_interval:
                time.sleep(min_interval - time_since_last)
        self.last_request_time = time.time()
    
    def _safe_get_data(self, ticker_obj, method_name: str, *args, **kwargs) -> Optional[Any]:
        """
        Safely get data from ticker object with error handling
        
        Args:
            ticker_obj: yfinance Ticker object
            method_name: Name of the method/property to call
            *args, **kwargs: Arguments to pass to the method
            
        Returns:
            Data if successful, None if failed
        """
        try:
            if hasattr(ticker_obj, method_name):
                attr = getattr(ticker_obj, method_name)
                if callable(attr):
                    return attr(*args, **kwargs)
                else:
                    return attr
            return None
        except Exception as e:
            self.logger.warning(f"Failed to get {method_name}: {str(e)}")
            return None
    
    def fetch_stock_info(self, symbol: str) -> Dict[str, Any]:
        """
        Fetch basic stock information
        
        Args:
            symbol: Stock symbol (e.g., 'AAPL')
            
        Returns:
            Dictionary containing stock info
        """
        self._rate_limit_wait()
        
        try:
            ticker = yf.Ticker(symbol)
            info = self._safe_get_data(ticker, 'info')
            
            if info:
                # Clean and standardize the info data
                cleaned_info = {}
                for key, value in info.items():
                    if value is not None and value != 'None':
                        # Convert numpy types to native Python types
                        if isinstance(value, (np.integer, np.floating)):
                            value = value.item()
                        elif isinstance(value, np.ndarray):
                            value = value.tolist()
                        cleaned_info[key] = value
                
                return {
                    'symbol': symbol,
                    'fetch_timestamp': datetime.now().isoformat(),
                    'info': cleaned_info
                }
            else:
                self.logger.warning(f"No info data found for {symbol}")
                return {'symbol': symbol, 'fetch_timestamp': datetime.now().isoformat(), 'info': {}}
                
        except Exception as e:
            self.logger.error(f"Error fetching info for {symbol}: {str(e)}")
            return {'symbol': symbol, 'fetch_timestamp': datetime.now().isoformat(), 'info': {}, 'error': str(e)}
    
    def fetch_price_history(self, symbol: str, period: str = DEFAULT_PERIOD, 
                          interval: str = DEFAULT_INTERVAL) -> Dict[str, Any]:
        """
        Fetch historical price data
        
        Args:
            symbol: Stock symbol
            period: Period for historical data (1d, 5d, 1mo, 3mo, 6mo, 1y, 2y, 5y, 10y, ytd, max)
            interval: Data interval (1m, 2m, 5m, 15m, 30m, 60m, 90m, 1h, 1d, 5d, 1wk, 1mo, 3mo)
            
        Returns:
            Dictionary containing price history
        """
        self._rate_limit_wait()
        
        try:
            ticker = yf.Ticker(symbol)
            history = self._safe_get_data(ticker, 'history', period=period, interval=interval)
            
            if history is not None and not history.empty:
                # Convert DataFrame to records
                history_records = []
                for date, row in history.iterrows():
                    record = {
                        'date': date.isoformat() if hasattr(date, 'isoformat') else str(date),
                        'open': float(row['Open']) if pd.notna(row['Open']) else None,
                        'high': float(row['High']) if pd.notna(row['High']) else None,
                        'low': float(row['Low']) if pd.notna(row['Low']) else None,
                        'close': float(row['Close']) if pd.notna(row['Close']) else None,
                        'volume': int(row['Volume']) if pd.notna(row['Volume']) else None,
                    }
                    # Add dividends and stock splits if available
                    if 'Dividends' in row:
                        record['dividends'] = float(row['Dividends']) if pd.notna(row['Dividends']) else None
                    if 'Stock Splits' in row:
                        record['stock_splits'] = float(row['Stock Splits']) if pd.notna(row['Stock Splits']) else None
                    
                    history_records.append(record)
                
                return {
                    'symbol': symbol,
                    'period': period,
                    'interval': interval,
                    'fetch_timestamp': datetime.now().isoformat(),
                    'history': history_records
                }
            else:
                self.logger.warning(f"No price history found for {symbol}")
                return {
                    'symbol': symbol,
                    'period': period,
                    'interval': interval,
                    'fetch_timestamp': datetime.now().isoformat(),
                    'history': []
                }
                
        except Exception as e:
            self.logger.error(f"Error fetching price history for {symbol}: {str(e)}")
            return {
                'symbol': symbol,
                'period': period,
                'interval': interval,
                'fetch_timestamp': datetime.now().isoformat(),
                'history': [],
                'error': str(e)
            }

    def fetch_financials(self, symbol: str) -> Dict[str, Any]:
        """
        Fetch financial statements (annual and quarterly)

        Args:
            symbol: Stock symbol

        Returns:
            Dictionary containing financial data
        """
        self._rate_limit_wait()

        try:
            ticker = yf.Ticker(symbol)

            # Get annual and quarterly financials
            financials = self._safe_get_data(ticker, 'financials')
            quarterly_financials = self._safe_get_data(ticker, 'quarterly_financials')

            result = {
                'symbol': symbol,
                'fetch_timestamp': datetime.now().isoformat(),
                'annual_financials': [],
                'quarterly_financials': []
            }

            # Process annual financials
            if financials is not None and not financials.empty:
                for date, column in financials.items():
                    financial_record = {
                        'date': date.isoformat() if hasattr(date, 'isoformat') else str(date),
                        'data': {}
                    }
                    for index, value in column.items():
                        if pd.notna(value):
                            financial_record['data'][str(index)] = float(value) if isinstance(value, (int, float)) else str(value)
                    result['annual_financials'].append(financial_record)

            # Process quarterly financials
            if quarterly_financials is not None and not quarterly_financials.empty:
                for date, column in quarterly_financials.items():
                    financial_record = {
                        'date': date.isoformat() if hasattr(date, 'isoformat') else str(date),
                        'data': {}
                    }
                    for index, value in column.items():
                        if pd.notna(value):
                            financial_record['data'][str(index)] = float(value) if isinstance(value, (int, float)) else str(value)
                    result['quarterly_financials'].append(financial_record)

            return result

        except Exception as e:
            self.logger.error(f"Error fetching financials for {symbol}: {str(e)}")
            return {
                'symbol': symbol,
                'fetch_timestamp': datetime.now().isoformat(),
                'annual_financials': [],
                'quarterly_financials': [],
                'error': str(e)
            }

    def fetch_balance_sheet(self, symbol: str) -> Dict[str, Any]:
        """
        Fetch balance sheet data (annual and quarterly)

        Args:
            symbol: Stock symbol

        Returns:
            Dictionary containing balance sheet data
        """
        self._rate_limit_wait()

        try:
            ticker = yf.Ticker(symbol)

            balance_sheet = self._safe_get_data(ticker, 'balance_sheet')
            quarterly_balance_sheet = self._safe_get_data(ticker, 'quarterly_balance_sheet')

            result = {
                'symbol': symbol,
                'fetch_timestamp': datetime.now().isoformat(),
                'annual_balance_sheet': [],
                'quarterly_balance_sheet': []
            }

            # Process annual balance sheet
            if balance_sheet is not None and not balance_sheet.empty:
                for date, column in balance_sheet.items():
                    bs_record = {
                        'date': date.isoformat() if hasattr(date, 'isoformat') else str(date),
                        'data': {}
                    }
                    for index, value in column.items():
                        if pd.notna(value):
                            bs_record['data'][str(index)] = float(value) if isinstance(value, (int, float)) else str(value)
                    result['annual_balance_sheet'].append(bs_record)

            # Process quarterly balance sheet
            if quarterly_balance_sheet is not None and not quarterly_balance_sheet.empty:
                for date, column in quarterly_balance_sheet.items():
                    bs_record = {
                        'date': date.isoformat() if hasattr(date, 'isoformat') else str(date),
                        'data': {}
                    }
                    for index, value in column.items():
                        if pd.notna(value):
                            bs_record['data'][str(index)] = float(value) if isinstance(value, (int, float)) else str(value)
                    result['quarterly_balance_sheet'].append(bs_record)

            return result

        except Exception as e:
            self.logger.error(f"Error fetching balance sheet for {symbol}: {str(e)}")
            return {
                'symbol': symbol,
                'fetch_timestamp': datetime.now().isoformat(),
                'annual_balance_sheet': [],
                'quarterly_balance_sheet': [],
                'error': str(e)
            }

    def fetch_cashflow(self, symbol: str) -> Dict[str, Any]:
        """
        Fetch cash flow data (annual and quarterly)

        Args:
            symbol: Stock symbol

        Returns:
            Dictionary containing cash flow data
        """
        self._rate_limit_wait()

        try:
            ticker = yf.Ticker(symbol)

            cashflow = self._safe_get_data(ticker, 'cashflow')
            quarterly_cashflow = self._safe_get_data(ticker, 'quarterly_cashflow')

            result = {
                'symbol': symbol,
                'fetch_timestamp': datetime.now().isoformat(),
                'annual_cashflow': [],
                'quarterly_cashflow': []
            }

            # Process annual cash flow
            if cashflow is not None and not cashflow.empty:
                for date, column in cashflow.items():
                    cf_record = {
                        'date': date.isoformat() if hasattr(date, 'isoformat') else str(date),
                        'data': {}
                    }
                    for index, value in column.items():
                        if pd.notna(value):
                            cf_record['data'][str(index)] = float(value) if isinstance(value, (int, float)) else str(value)
                    result['annual_cashflow'].append(cf_record)

            # Process quarterly cash flow
            if quarterly_cashflow is not None and not quarterly_cashflow.empty:
                for date, column in quarterly_cashflow.items():
                    cf_record = {
                        'date': date.isoformat() if hasattr(date, 'isoformat') else str(date),
                        'data': {}
                    }
                    for index, value in column.items():
                        if pd.notna(value):
                            cf_record['data'][str(index)] = float(value) if isinstance(value, (int, float)) else str(value)
                    result['quarterly_cashflow'].append(cf_record)

            return result

        except Exception as e:
            self.logger.error(f"Error fetching cash flow for {symbol}: {str(e)}")
            return {
                'symbol': symbol,
                'fetch_timestamp': datetime.now().isoformat(),
                'annual_cashflow': [],
                'quarterly_cashflow': [],
                'error': str(e)
            }

    def fetch_earnings(self, symbol: str) -> Dict[str, Any]:
        """
        Fetch earnings data using the new API methods

        Args:
            symbol: Stock symbol

        Returns:
            Dictionary containing earnings data
        """
        self._rate_limit_wait()

        try:
            ticker = yf.Ticker(symbol)

            result = {
                'symbol': symbol,
                'fetch_timestamp': datetime.now().isoformat(),
                'annual_earnings': [],
                'quarterly_earnings': [],
                'earnings_calendar': [],
                'income_stmt': [],
                'quarterly_income_stmt': []
            }

            # Use the new income statement API instead of deprecated earnings
            try:
                # Get annual income statement (contains Net Income which is earnings)
                income_stmt = self._safe_get_data(ticker, 'income_stmt')
                if income_stmt is not None and hasattr(income_stmt, 'empty') and not income_stmt.empty:
                    for date_col in income_stmt.columns:
                        income_record = {
                            'date': date_col.isoformat() if hasattr(date_col, 'isoformat') else str(date_col),
                            'data': {}
                        }

                        for index, value in income_stmt[date_col].items():
                            if pd.notna(value):
                                # Convert to float if it's a number
                                try:
                                    income_record['data'][str(index)] = float(value)
                                except (ValueError, TypeError):
                                    income_record['data'][str(index)] = str(value)

                        result['income_stmt'].append(income_record)

                        # Extract key earnings metrics for backward compatibility
                        net_income = income_record['data'].get('Net Income')
                        total_revenue = income_record['data'].get('Total Revenue')

                        if net_income is not None or total_revenue is not None:
                            earnings_record = {
                                'year': str(date_col.year) if hasattr(date_col, 'year') else str(date_col),
                                'revenue': total_revenue,
                                'earnings': net_income
                            }
                            result['annual_earnings'].append(earnings_record)

            except Exception as e:
                self.logger.debug(f"Could not fetch income statement for {symbol}: {str(e)}")

            # Get quarterly income statement
            try:
                quarterly_income_stmt = self._safe_get_data(ticker, 'quarterly_income_stmt')
                if quarterly_income_stmt is not None and hasattr(quarterly_income_stmt, 'empty') and not quarterly_income_stmt.empty:
                    for date_col in quarterly_income_stmt.columns:
                        income_record = {
                            'date': date_col.isoformat() if hasattr(date_col, 'isoformat') else str(date_col),
                            'data': {}
                        }

                        for index, value in quarterly_income_stmt[date_col].items():
                            if pd.notna(value):
                                try:
                                    income_record['data'][str(index)] = float(value)
                                except (ValueError, TypeError):
                                    income_record['data'][str(index)] = str(value)

                        result['quarterly_income_stmt'].append(income_record)

                        # Extract key earnings metrics for backward compatibility
                        net_income = income_record['data'].get('Net Income')
                        total_revenue = income_record['data'].get('Total Revenue')

                        if net_income is not None or total_revenue is not None:
                            earnings_record = {
                                'quarter': str(date_col),
                                'revenue': total_revenue,
                                'earnings': net_income
                            }
                            result['quarterly_earnings'].append(earnings_record)

            except Exception as e:
                self.logger.debug(f"Could not fetch quarterly income statement for {symbol}: {str(e)}")

            # Try to get earnings calendar (this might still work)
            try:
                earnings_dates = self._safe_get_data(ticker, 'calendar')
                if earnings_dates is not None:
                    if hasattr(earnings_dates, 'empty') and not earnings_dates.empty:
                        # DataFrame format
                        for index, row in earnings_dates.iterrows():
                            calendar_record = {
                                'date': index.isoformat() if hasattr(index, 'isoformat') else str(index)
                            }
                            for col, value in row.items():
                                if pd.notna(value):
                                    calendar_record[str(col)] = float(value) if isinstance(value, (int, float)) else str(value)
                            result['earnings_calendar'].append(calendar_record)
                    elif isinstance(earnings_dates, dict):
                        # Dict format - handle the actual structure
                        if earnings_dates:  # Only process if dict is not empty
                            for key, value in earnings_dates.items():
                                calendar_record = {
                                    'date': str(key),
                                    'data': value if isinstance(value, dict) else {'value': value}
                                }
                                result['earnings_calendar'].append(calendar_record)
                        else:
                            self.logger.debug(f"Earnings calendar is empty dict for {symbol}")
            except Exception as e:
                self.logger.debug(f"Could not fetch earnings calendar for {symbol}: {str(e)}")

            return result

        except Exception as e:
            self.logger.error(f"Error fetching earnings for {symbol}: {str(e)}")
            return {
                'symbol': symbol,
                'fetch_timestamp': datetime.now().isoformat(),
                'annual_earnings': [],
                'quarterly_earnings': [],
                'earnings_calendar': [],
                'income_stmt': [],
                'quarterly_income_stmt': [],
                'error': str(e)
            }

    def fetch_additional_data(self, symbol: str) -> Dict[str, Any]:
        """
        Fetch additional data like recommendations, holders, dividends, splits

        Args:
            symbol: Stock symbol

        Returns:
            Dictionary containing additional data
        """
        self._rate_limit_wait()

        try:
            ticker = yf.Ticker(symbol)

            result = {
                'symbol': symbol,
                'fetch_timestamp': datetime.now().isoformat(),
                'recommendations': [],
                'major_holders': [],
                'institutional_holders': [],
                'mutualfund_holders': [],
                'dividends': [],
                'splits': [],
                'actions': []
            }

            # Fetch recommendations
            recommendations = self._safe_get_data(ticker, 'recommendations')
            if recommendations is not None and not recommendations.empty:
                for index, row in recommendations.iterrows():
                    rec_record = {
                        'date': index.isoformat() if hasattr(index, 'isoformat') else str(index)
                    }
                    for col, value in row.items():
                        if pd.notna(value):
                            rec_record[str(col)] = str(value)
                    result['recommendations'].append(rec_record)

            # Fetch major holders
            major_holders = self._safe_get_data(ticker, 'major_holders')
            if major_holders is not None and not major_holders.empty:
                for index, row in major_holders.iterrows():
                    holder_record = {}
                    for col, value in row.items():
                        if pd.notna(value):
                            holder_record[str(col)] = str(value)
                    result['major_holders'].append(holder_record)

            # Fetch institutional holders
            institutional_holders = self._safe_get_data(ticker, 'institutional_holders')
            if institutional_holders is not None and not institutional_holders.empty:
                for index, row in institutional_holders.iterrows():
                    inst_record = {}
                    for col, value in row.items():
                        if pd.notna(value):
                            if isinstance(value, (int, float)):
                                inst_record[str(col)] = float(value)
                            else:
                                inst_record[str(col)] = str(value)
                    result['institutional_holders'].append(inst_record)

            # Fetch mutual fund holders
            mutualfund_holders = self._safe_get_data(ticker, 'mutualfund_holders')
            if mutualfund_holders is not None and not mutualfund_holders.empty:
                for index, row in mutualfund_holders.iterrows():
                    mf_record = {}
                    for col, value in row.items():
                        if pd.notna(value):
                            if isinstance(value, (int, float)):
                                mf_record[str(col)] = float(value)
                            else:
                                mf_record[str(col)] = str(value)
                    result['mutualfund_holders'].append(mf_record)

            # Fetch dividends
            dividends = self._safe_get_data(ticker, 'dividends')
            if dividends is not None and not dividends.empty:
                for date, value in dividends.items():
                    if pd.notna(value):
                        result['dividends'].append({
                            'date': date.isoformat() if hasattr(date, 'isoformat') else str(date),
                            'dividend': float(value)
                        })

            # Fetch splits
            splits = self._safe_get_data(ticker, 'splits')
            if splits is not None and not splits.empty:
                for date, value in splits.items():
                    if pd.notna(value):
                        result['splits'].append({
                            'date': date.isoformat() if hasattr(date, 'isoformat') else str(date),
                            'split_ratio': float(value)
                        })

            # Fetch actions (dividends + splits combined)
            actions = self._safe_get_data(ticker, 'actions')
            if actions is not None and not actions.empty:
                for date, row in actions.iterrows():
                    action_record = {
                        'date': date.isoformat() if hasattr(date, 'isoformat') else str(date)
                    }
                    for col, value in row.items():
                        if pd.notna(value):
                            action_record[str(col)] = float(value)
                    result['actions'].append(action_record)

            return result

        except Exception as e:
            self.logger.error(f"Error fetching additional data for {symbol}: {str(e)}")
            return {
                'symbol': symbol,
                'fetch_timestamp': datetime.now().isoformat(),
                'recommendations': [],
                'major_holders': [],
                'institutional_holders': [],
                'mutualfund_holders': [],
                'dividends': [],
                'splits': [],
                'actions': [],
                'error': str(e)
            }

    def fetch_comprehensive_data(self, symbol: str, include_history: bool = True,
                               period: str = DEFAULT_PERIOD, interval: str = DEFAULT_INTERVAL) -> Dict[str, Any]:
        """
        Fetch all available data for a single stock

        Args:
            symbol: Stock symbol
            include_history: Whether to include price history (can be large)
            period: Period for historical data
            interval: Interval for historical data

        Returns:
            Dictionary containing all available stock data
        """
        self.logger.info(f"Fetching comprehensive data for {symbol}")

        result = {
            'symbol': symbol,
            'fetch_timestamp': datetime.now().isoformat(),
            'data_types_fetched': [],
            'errors': []
        }

        # Fetch basic info
        try:
            info_data = self.fetch_stock_info(symbol)
            result['info'] = info_data.get('info', {})
            if 'error' in info_data:
                result['errors'].append(f"Info: {info_data['error']}")
            else:
                result['data_types_fetched'].append('info')
        except Exception as e:
            result['errors'].append(f"Info: {str(e)}")

        # Fetch price history if requested
        if include_history:
            try:
                history_data = self.fetch_price_history(symbol, period, interval)
                result['price_history'] = history_data.get('history', [])
                if 'error' in history_data:
                    result['errors'].append(f"Price History: {history_data['error']}")
                else:
                    result['data_types_fetched'].append('price_history')
            except Exception as e:
                result['errors'].append(f"Price History: {str(e)}")

        # Fetch financials
        try:
            financials_data = self.fetch_financials(symbol)
            result['financials'] = {
                'annual': financials_data.get('annual_financials', []),
                'quarterly': financials_data.get('quarterly_financials', [])
            }
            if 'error' in financials_data:
                result['errors'].append(f"Financials: {financials_data['error']}")
            else:
                result['data_types_fetched'].append('financials')
        except Exception as e:
            result['errors'].append(f"Financials: {str(e)}")

        # Fetch balance sheet
        try:
            balance_sheet_data = self.fetch_balance_sheet(symbol)
            result['balance_sheet'] = {
                'annual': balance_sheet_data.get('annual_balance_sheet', []),
                'quarterly': balance_sheet_data.get('quarterly_balance_sheet', [])
            }
            if 'error' in balance_sheet_data:
                result['errors'].append(f"Balance Sheet: {balance_sheet_data['error']}")
            else:
                result['data_types_fetched'].append('balance_sheet')
        except Exception as e:
            result['errors'].append(f"Balance Sheet: {str(e)}")

        # Fetch cash flow
        try:
            cashflow_data = self.fetch_cashflow(symbol)
            result['cashflow'] = {
                'annual': cashflow_data.get('annual_cashflow', []),
                'quarterly': cashflow_data.get('quarterly_cashflow', [])
            }
            if 'error' in cashflow_data:
                result['errors'].append(f"Cash Flow: {cashflow_data['error']}")
            else:
                result['data_types_fetched'].append('cashflow')
        except Exception as e:
            result['errors'].append(f"Cash Flow: {str(e)}")

        # Fetch earnings
        try:
            earnings_data = self.fetch_earnings(symbol)
            result['earnings'] = {
                'annual': earnings_data.get('annual_earnings', []),
                'quarterly': earnings_data.get('quarterly_earnings', []),
                'calendar': earnings_data.get('earnings_calendar', [])
            }
            if 'error' in earnings_data:
                result['errors'].append(f"Earnings: {earnings_data['error']}")
            else:
                result['data_types_fetched'].append('earnings')
        except Exception as e:
            result['errors'].append(f"Earnings: {str(e)}")

        # Fetch additional data
        try:
            additional_data = self.fetch_additional_data(symbol)
            result['additional_data'] = {
                'recommendations': additional_data.get('recommendations', []),
                'major_holders': additional_data.get('major_holders', []),
                'institutional_holders': additional_data.get('institutional_holders', []),
                'mutualfund_holders': additional_data.get('mutualfund_holders', []),
                'dividends': additional_data.get('dividends', []),
                'splits': additional_data.get('splits', []),
                'actions': additional_data.get('actions', [])
            }
            if 'error' in additional_data:
                result['errors'].append(f"Additional Data: {additional_data['error']}")
            else:
                result['data_types_fetched'].append('additional_data')
        except Exception as e:
            result['errors'].append(f"Additional Data: {str(e)}")

        self.logger.info(f"Completed fetching data for {symbol}. Data types: {result['data_types_fetched']}")
        if result['errors']:
            self.logger.warning(f"Errors encountered for {symbol}: {result['errors']}")

        return result

    def fetch_multiple_stocks(self, symbols: List[str], include_history: bool = True,
                            period: str = DEFAULT_PERIOD, interval: str = DEFAULT_INTERVAL,
                            max_workers: int = MAX_CONCURRENT_REQUESTS) -> Dict[str, Any]:
        """
        Fetch comprehensive data for multiple stocks with concurrent processing

        Args:
            symbols: List of stock symbols
            include_history: Whether to include price history
            period: Period for historical data
            interval: Interval for historical data
            max_workers: Maximum number of concurrent workers

        Returns:
            Dictionary containing data for all stocks
        """
        self.logger.info(f"Starting batch fetch for {len(symbols)} stocks")

        results = {
            'batch_timestamp': datetime.now().isoformat(),
            'total_symbols': len(symbols),
            'successful_fetches': 0,
            'failed_fetches': 0,
            'stocks': {}
        }

        # Process stocks in batches to avoid overwhelming the API
        batch_size = min(BATCH_SIZE, len(symbols))

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_symbol = {}

            for i in range(0, len(symbols), batch_size):
                batch_symbols = symbols[i:i + batch_size]

                for symbol in batch_symbols:
                    future = executor.submit(
                        self.fetch_comprehensive_data,
                        symbol,
                        include_history,
                        period,
                        interval
                    )
                    future_to_symbol[future] = symbol

                # Add delay between batches
                if i + batch_size < len(symbols):
                    time.sleep(1)

            # Collect results with progress bar
            with tqdm(total=len(symbols), desc="Fetching stock data") as pbar:
                for future in as_completed(future_to_symbol):
                    symbol = future_to_symbol[future]
                    try:
                        stock_data = future.result(timeout=REQUEST_TIMEOUT)
                        results['stocks'][symbol] = stock_data

                        if stock_data.get('errors'):
                            results['failed_fetches'] += 1
                            self.logger.warning(f"Partial failure for {symbol}: {stock_data['errors']}")
                        else:
                            results['successful_fetches'] += 1

                    except Exception as e:
                        results['failed_fetches'] += 1
                        results['stocks'][symbol] = {
                            'symbol': symbol,
                            'fetch_timestamp': datetime.now().isoformat(),
                            'error': str(e)
                        }
                        self.logger.error(f"Failed to fetch data for {symbol}: {str(e)}")

                    pbar.update(1)

        self.logger.info(f"Batch fetch completed. Success: {results['successful_fetches']}, "
                        f"Failed: {results['failed_fetches']}")

        return results

    def get_sp500_symbols(self) -> List[str]:
        """
        Get list of S&P 500 stock symbols

        Returns:
            List of S&P 500 symbols
        """
        try:
            # This is a simple way to get S&P 500 symbols
            # In production, you might want to use a more reliable source
            url = "https://en.wikipedia.org/wiki/List_of_S%26P_500_companies"
            tables = pd.read_html(url)
            sp500_table = tables[0]
            symbols = sp500_table['Symbol'].tolist()

            # Clean symbols (remove dots, etc.)
            cleaned_symbols = []
            for symbol in symbols:
                # Replace dots with dashes (Yahoo Finance format)
                cleaned_symbol = str(symbol).replace('.', '-')
                cleaned_symbols.append(cleaned_symbol)

            self.logger.info(f"Retrieved {len(cleaned_symbols)} S&P 500 symbols")
            return cleaned_symbols

        except Exception as e:
            self.logger.error(f"Error fetching S&P 500 symbols: {str(e)}")
            # Return a small sample if we can't fetch the full list
            return ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'BRK-B', 'JNJ', 'V']

    def validate_symbol(self, symbol: str) -> bool:
        """
        Validate if a stock symbol exists and has data

        Args:
            symbol: Stock symbol to validate

        Returns:
            True if symbol is valid, False otherwise
        """
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info

            # Check if we got meaningful data
            if info and len(info) > 1 and 'symbol' in info:
                return True
            return False

        except Exception as e:
            self.logger.debug(f"Symbol validation failed for {symbol}: {str(e)}")
            return False
