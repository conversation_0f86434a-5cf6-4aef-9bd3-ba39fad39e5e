#!/usr/bin/env python3
"""
Example usage of the Yahoo Finance Stock Data Fetcher
Demonstrates various ways to use the system
"""

import json
from datetime import datetime
from stock_data_manager import StockDataManager
from utils import format_large_number


def example_single_stock():
    """Example: Fetch data for a single stock"""
    print("=== Single Stock Example ===")
    
    # Initialize the manager
    manager = StockDataManager()
    
    # Fetch comprehensive data for Apple
    symbol = 'AAPL'
    print(f"Fetching comprehensive data for {symbol}...")
    
    result = manager.fetch_and_store_stock(
        symbol=symbol,
        include_history=True,
        period='1y',  # 1 year of history
        interval='1d'  # Daily intervals
    )
    
    print(f"Status: {result['status']}")
    if result['status'] == 'success':
        print(f"Data types fetched: {', '.join(result['data_types_fetched'])}")
    elif result.get('errors'):
        print(f"Errors: {'; '.join(result['errors'])}")
    
    # Retrieve and display the stored data
    print(f"\nRetrieving stored data for {symbol}...")
    stock_data = manager.get_stock_data(symbol)
    
    if stock_data:
        info = stock_data['info']['info']
        print(f"Company: {info.get('longName', 'N/A')}")
        print(f"Sector: {info.get('sector', 'N/A')}")
        print(f"Industry: {info.get('industry', 'N/A')}")
        print(f"Market Cap: {format_large_number(info.get('marketCap'))}")
        print(f"Price history records: {len(stock_data['price_history'])}")
        
        if stock_data['price_history']:
            latest = stock_data['price_history'][-1]
            print(f"Latest price: ${latest['close']} on {latest['date']}")
    
    print()


def example_multiple_stocks():
    """Example: Fetch data for multiple stocks"""
    print("=== Multiple Stocks Example ===")
    
    manager = StockDataManager()
    
    # Define a list of tech stocks
    tech_stocks = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA']
    
    print(f"Fetching data for {len(tech_stocks)} tech stocks...")
    print(f"Symbols: {', '.join(tech_stocks)}")
    
    # Fetch data for multiple stocks
    results = manager.fetch_and_store_multiple_stocks(
        symbols=tech_stocks,
        include_history=False,  # Skip history for faster processing
        max_workers=3  # Use 3 concurrent workers
    )
    
    print(f"\nBatch Results:")
    print(f"Total symbols: {results['total_symbols']}")
    print(f"Successful: {results['successful']}")
    print(f"Failed: {results['failed']}")
    print(f"Skipped: {results['skipped']}")
    
    if results['successful'] > 0:
        success_rate = (results['successful'] / results['total_symbols']) * 100
        print(f"Success rate: {success_rate:.1f}%")
    
    # Show details for successful fetches
    successful_symbols = []
    for symbol, result in results['results'].items():
        if result['status'] == 'success':
            successful_symbols.append(symbol)
    
    if successful_symbols:
        print(f"\nSuccessfully processed: {', '.join(successful_symbols)}")
    
    print()


def example_search_and_analysis():
    """Example: Search stocks and perform basic analysis"""
    print("=== Search and Analysis Example ===")
    
    manager = StockDataManager()
    
    # Search for technology stocks
    print("Searching for technology stocks...")
    tech_results = manager.search_stocks("technology", limit=10)
    
    if tech_results:
        print(f"Found {len(tech_results)} technology stocks:")
        for i, stock in enumerate(tech_results[:5], 1):
            print(f"{i}. {stock['symbol']:6s} - {stock['company_name'][:40]:40s} "
                  f"(Market Cap: {format_large_number(stock['market_cap'])})")
    
    # Search for a specific company
    print("\nSearching for 'Apple'...")
    apple_results = manager.search_stocks("Apple")
    
    if apple_results:
        for stock in apple_results[:3]:
            print(f"  {stock['symbol']:6s} - {stock['company_name']}")
    
    print()


def example_database_stats():
    """Example: Show database statistics"""
    print("=== Database Statistics Example ===")
    
    manager = StockDataManager()
    
    stats = manager.get_database_stats()
    
    print("Database Statistics:")
    print(f"  Stocks: {stats.get('stocks_count', 0):,}")
    print(f"  Price History Records: {stats.get('price_history_count', 0):,}")
    print(f"  Financial Reports: {stats.get('financials_count', 0):,}")
    print(f"  Earnings Records: {stats.get('earnings_count', 0):,}")
    print(f"  Total Fetch Operations: {stats.get('fetch_log_count', 0):,}")
    
    date_range = stats.get('price_data_date_range', {})
    if date_range.get('start') and date_range.get('end'):
        print(f"  Price Data Range: {date_range['start']} to {date_range['end']}")
    
    if stats.get('last_fetch'):
        print(f"  Last Fetch: {stats['last_fetch']}")
    
    print()


def example_detailed_stock_analysis():
    """Example: Detailed analysis of a specific stock"""
    print("=== Detailed Stock Analysis Example ===")
    
    manager = StockDataManager()
    symbol = 'AAPL'
    
    # First ensure we have data
    print(f"Ensuring we have recent data for {symbol}...")
    fetch_result = manager.fetch_and_store_stock(symbol, force_refresh=False)
    
    if fetch_result['status'] not in ['success', 'skipped']:
        print(f"Failed to fetch data: {fetch_result}")
        return
    
    # Get comprehensive data
    stock_data = manager.get_stock_data(symbol)
    
    if not stock_data:
        print(f"No data available for {symbol}")
        return
    
    info = stock_data['info']['info']
    price_history = stock_data['price_history']
    financials = stock_data['financials']
    earnings = stock_data['earnings']
    
    print(f"\n=== Detailed Analysis for {symbol} ===")
    
    # Basic company info
    print(f"Company: {info.get('longName', 'N/A')}")
    print(f"Sector: {info.get('sector', 'N/A')}")
    print(f"Industry: {info.get('industry', 'N/A')}")
    print(f"Website: {info.get('website', 'N/A')}")
    print(f"Employees: {info.get('fullTimeEmployees', 'N/A'):,}" if info.get('fullTimeEmployees') else "Employees: N/A")
    
    # Financial metrics
    print(f"\nKey Metrics:")
    print(f"  Market Cap: {format_large_number(info.get('marketCap'))}")
    print(f"  Enterprise Value: {format_large_number(info.get('enterpriseValue'))}")
    print(f"  P/E Ratio: {info.get('trailingPE', 'N/A')}")
    print(f"  P/B Ratio: {info.get('priceToBook', 'N/A')}")
    print(f"  Dividend Yield: {info.get('dividendYield', 'N/A')}")
    
    # Price analysis
    if price_history:
        print(f"\nPrice Analysis ({len(price_history)} records):")
        latest = price_history[-1]
        oldest = price_history[0]
        
        print(f"  Latest Price: ${latest['close']:.2f} ({latest['date']})")
        print(f"  Oldest Price: ${oldest['close']:.2f} ({oldest['date']})")
        
        if oldest['close'] and latest['close']:
            change = ((latest['close'] - oldest['close']) / oldest['close']) * 100
            print(f"  Total Return: {change:+.1f}%")
        
        # Calculate some basic statistics
        prices = [record['close'] for record in price_history if record['close']]
        if prices:
            print(f"  Highest: ${max(prices):.2f}")
            print(f"  Lowest: ${min(prices):.2f}")
            print(f"  Average: ${sum(prices)/len(prices):.2f}")
    
    # Financial statements summary
    annual_financials = financials.get('annual', [])
    if annual_financials:
        print(f"\nFinancial Statements: {len(annual_financials)} annual reports")
        latest_financial = annual_financials[0]  # Most recent
        print(f"  Latest Report: {latest_financial['date']}")
    
    # Earnings summary
    annual_earnings = earnings.get('annual', [])
    if annual_earnings:
        print(f"\nEarnings: {len(annual_earnings)} annual reports")
        for i, earning in enumerate(annual_earnings[:3]):  # Show last 3 years
            revenue = format_large_number(earning['revenue']) if earning['revenue'] else 'N/A'
            net_earnings = format_large_number(earning['earnings']) if earning['earnings'] else 'N/A'
            print(f"  {earning['year']}: Revenue {revenue}, Earnings {net_earnings}")
    
    print()


def main():
    """Run all examples"""
    print("Yahoo Finance Stock Data Fetcher - Usage Examples")
    print("=" * 60)
    
    try:
        # Run examples
        example_single_stock()
        example_multiple_stocks()
        example_search_and_analysis()
        example_database_stats()
        example_detailed_stock_analysis()
        
        print("All examples completed successfully!")
        
    except KeyboardInterrupt:
        print("\nExamples interrupted by user")
    except Exception as e:
        print(f"\nError running examples: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
