# Yahoo Finance Stock Data Fetcher

A comprehensive Python program that pulls all available data from the Yahoo Finance API, including stock prices, financial statements, earnings reports, and more. The program can handle both single stock queries and bulk operations for thousands of stocks, with proper data storage and indexing.

## Features

- **Comprehensive Data Collection**: Fetches all available data from Yahoo Finance API
  - Stock information and company details
  - Historical price data with customizable periods and intervals
  - Financial statements (annual and quarterly)
  - Balance sheets (annual and quarterly)
  - Cash flow statements (annual and quarterly)
  - Earnings data and earnings calendar
  - Analyst recommendations
  - Institutional and mutual fund holders
  - Dividends and stock splits history

- **Scalable Operations**: Works for single stocks or thousands of stocks
  - Single stock fetching
  - Bulk operations with concurrent processing
  - S&P 500 data fetching
  - Rate limiting and error handling

- **Data Storage & Indexing**: SQLite database with proper indexing
  - Normalized database schema
  - Efficient indexing for fast queries
  - Data validation and cleaning
  - **Comprehensive duplicate prevention** - Safe to run multiple times

- **Smart Data Management**:
  - Automatic data freshness checking
  - Configurable refresh intervals
  - Comprehensive error handling and logging
  - Progress tracking for bulk operations

## Installation

1. Clone or download the repository
2. Install required dependencies:

```bash
pip install -r requirements.txt
```

## Quick Start

### Single Stock Data

```bash
# Fetch comprehensive data for Apple
python main.py --symbol AAPL

# Fetch without price history (faster)
python main.py --symbol AAPL --no-history

# Force refresh even if data is recent
python main.py --symbol AAPL --force-refresh
```

### Multiple Stocks

```bash
# Fetch data for multiple stocks
python main.py --symbols AAPL,MSFT,GOOGL,AMZN,TSLA

# Read symbols from file
python main.py --file stocks.txt

# Fetch all S&P 500 stocks
python main.py --sp500
```

### Database Operations

```bash
# Search stocks in database
python main.py --search "technology"
python main.py --search "AAPL"

# Get stored data for a specific stock
python main.py --get AAPL

# Show database statistics
python main.py --stats
```

## Configuration

Edit `config.py` to customize:

- Database settings
- Rate limiting parameters
- Data refresh intervals
- Logging configuration
- Default periods and intervals

## Database Schema

The program creates a SQLite database with the following tables:

- `stocks`: Master list of all stocks with basic info
- `stock_info`: Complete company information as JSON
- `price_history`: Historical price data
- `financials`: Income statements (annual/quarterly)
- `balance_sheet`: Balance sheet data (annual/quarterly)
- `cashflow`: Cash flow statements (annual/quarterly)
- `earnings`: Earnings data and calendar
- `recommendations`: Analyst recommendations
- `holders`: Institutional and mutual fund holders
- `dividends`: Dividend history
- `splits`: Stock split history
- `fetch_log`: Logging of all fetch operations

## API Usage Examples

### Using the StockDataManager Class

```python
from stock_data_manager import StockDataManager

# Initialize manager
manager = StockDataManager()

# Fetch single stock
result = manager.fetch_and_store_stock('AAPL')
print(f"Status: {result['status']}")

# Fetch multiple stocks
symbols = ['AAPL', 'MSFT', 'GOOGL']
results = manager.fetch_and_store_multiple_stocks(symbols)
print(f"Success rate: {results['successful']}/{results['total_symbols']}")

# Get stored data
stock_data = manager.get_stock_data('AAPL')
if stock_data:
    print(f"Company: {stock_data['info']['info']['longName']}")
    print(f"Latest price: ${stock_data['price_history'][-1]['close']}")

# Search stocks
search_results = manager.search_stocks('technology')
for stock in search_results[:5]:
    print(f"{stock['symbol']}: {stock['company_name']}")
```

### Using Individual Components

```python
from yahoo_finance_fetcher import YahooFinanceFetcher
from database_manager import DatabaseManager

# Initialize components
fetcher = YahooFinanceFetcher()
db = DatabaseManager()

# Fetch data
stock_data = fetcher.fetch_comprehensive_data('AAPL')

# Store in database
success = db.store_stock_data(stock_data)

# Retrieve from database
stored_info = db.get_stock_info('AAPL')
price_history = db.get_price_history('AAPL')
```

## Command Line Options

```
usage: main.py [-h] [--symbol SYMBOL] [--symbols SYMBOLS] [--file FILE]
               [--sp500] [--no-history] [--period PERIOD] [--interval INTERVAL]
               [--force-refresh] [--search SEARCH] [--get GET] [--stats]
               [--output OUTPUT] [--verbose] [--quiet] [--rate-limit RATE_LIMIT]
               [--max-workers MAX_WORKERS]

Options:
  --symbol, -s          Fetch data for a single stock symbol
  --symbols             Fetch data for multiple stocks (comma-separated)
  --file, -f            Read stock symbols from file (one per line)
  --sp500               Fetch data for all S&P 500 stocks
  --no-history          Skip price history (faster, smaller data)
  --period              Period for historical data (1d, 5d, 1mo, 3mo, 6mo, 1y, 2y, 5y, 10y, ytd, max)
  --interval            Interval for historical data (1m, 2m, 5m, 15m, 30m, 60m, 90m, 1h, 1d, 5d, 1wk, 1mo, 3mo)
  --force-refresh       Force refresh even if data is recent
  --search              Search stocks in database
  --get                 Get stored data for a specific stock symbol
  --stats               Show database statistics
  --output, -o          Save results to JSON file
  --verbose, -v         Enable verbose logging
  --quiet, -q           Suppress console output
  --rate-limit          Requests per second (default: 2)
  --max-workers         Maximum concurrent requests (default: 5)
```

## Error Handling

The program includes comprehensive error handling:

- **Rate Limiting**: Automatic rate limiting to respect Yahoo Finance API limits
- **Retry Logic**: Automatic retries for transient errors
- **Data Validation**: Input validation and data cleaning
- **Graceful Degradation**: Continues processing even if some stocks fail
- **Detailed Logging**: Comprehensive logging for debugging and monitoring

## Performance Considerations

- **Concurrent Processing**: Uses ThreadPoolExecutor for parallel requests
- **Rate Limiting**: Configurable rate limiting to avoid API throttling
- **Batch Processing**: Processes stocks in batches to manage memory usage
- **Database Indexing**: Proper indexing for fast queries
- **Data Freshness**: Avoids unnecessary API calls for recent data
- **Duplicate Prevention**: Safe to run multiple times without creating duplicates

## Data Freshness

The program automatically manages data freshness:

- Default refresh interval: 24 hours
- Earnings data refresh: 7 days
- Configurable refresh intervals in `config.py`
- Force refresh option available

## Duplicate Prevention

The system is fully protected against data duplication:

- **UNIQUE constraints** on all key data combinations (symbol+date, symbol+report_date, etc.)
- **INSERT OR REPLACE** statements ensure updates instead of duplicates
- **Safe to run multiple times** - no duplicate data will be created
- **Automatic data freshness checking** - skips unnecessary API calls
- **Comprehensive testing** - verified to prevent duplicates across all data types

You can safely run the same command multiple times:
```bash
# These commands won't create duplicates
python main.py --symbol AAPL
python main.py --symbol AAPL  # Safe to run again
python main.py --sp500        # Safe to run multiple times
```

## Logging

Comprehensive logging is available:

- Log file: `yahoo_finance_fetcher.log`
- Console output with different verbosity levels
- Error categorization and handling
- Performance metrics and timing

## File Structure

```
├── main.py                    # Main command-line interface
├── stock_data_manager.py      # Main orchestration class
├── yahoo_finance_fetcher.py   # Yahoo Finance API wrapper
├── database_manager.py        # SQLite database management
├── utils.py                   # Utility functions and validation
├── config.py                  # Configuration settings
├── requirements.txt           # Python dependencies
└── README.md                  # This file
```

## Contributing

Feel free to contribute by:

1. Reporting bugs or issues
2. Suggesting new features
3. Submitting pull requests
4. Improving documentation

## License

This project is open source. Please check the license file for details.

## Disclaimer

This tool is for educational and research purposes. Please respect Yahoo Finance's terms of service and rate limits. The authors are not responsible for any misuse of this tool or any consequences thereof.
