"""
Stock Data Manager
Main class that combines Yahoo Finance fetching with database storage
Handles both single stock and bulk operations with proper error handling and rate limiting
"""

import logging
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm

from yahoo_finance_fetcher import YahooFinanceFetcher
from database_manager import DatabaseManager
from config import *


class StockDataManager:
    """
    Main class for managing stock data fetching and storage
    Combines YahooFinanceFetcher and DatabaseManager for complete functionality
    """
    
    def __init__(self, db_path: str = DATABASE_PATH, rate_limit: float = REQUESTS_PER_SECOND):
        """
        Initialize the Stock Data Manager
        
        Args:
            db_path: Path to SQLite database
            rate_limit: Maximum requests per second for Yahoo Finance API
        """
        self.fetcher = YahooFinanceFetcher(rate_limit=rate_limit)
        self.db_manager = DatabaseManager(db_path=db_path)
        self.logger = self._setup_logging()
    
    def _setup_logging(self) -> logging.Logger:
        """Set up logging configuration"""
        logger = logging.getLogger(__name__)
        logger.setLevel(getattr(logging, LOG_LEVEL))
        
        if not logger.handlers:
            handler = logging.FileHandler(LOG_FILE)
            formatter = logging.Formatter(LOG_FORMAT)
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
            # Also log to console
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
        
        return logger
    
    def fetch_and_store_stock(self, symbol: str, include_history: bool = True,
                            period: str = DEFAULT_PERIOD, interval: str = DEFAULT_INTERVAL,
                            force_refresh: bool = False) -> Dict[str, Any]:
        """
        Fetch comprehensive data for a single stock and store in database
        
        Args:
            symbol: Stock symbol (e.g., 'AAPL')
            include_history: Whether to include price history
            period: Period for historical data
            interval: Interval for historical data
            force_refresh: Force refresh even if data is recent
            
        Returns:
            Dictionary containing fetch results and status
        """
        self.logger.info(f"Processing stock: {symbol}")
        
        # Check if we need to refresh data
        if not force_refresh and not self._needs_refresh(symbol):
            self.logger.info(f"Data for {symbol} is recent, skipping fetch")
            return {
                'symbol': symbol,
                'status': 'skipped',
                'reason': 'data_is_recent',
                'timestamp': datetime.now().isoformat()
            }
        
        try:
            # Fetch comprehensive data
            stock_data = self.fetcher.fetch_comprehensive_data(
                symbol=symbol,
                include_history=include_history,
                period=period,
                interval=interval
            )
            
            # Store in database
            success = self.db_manager.store_stock_data(stock_data)
            
            result = {
                'symbol': symbol,
                'status': 'success' if success else 'storage_failed',
                'data_types_fetched': stock_data.get('data_types_fetched', []),
                'errors': stock_data.get('errors', []),
                'timestamp': datetime.now().isoformat()
            }
            
            if not success:
                result['storage_error'] = 'Failed to store data in database'
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error processing {symbol}: {str(e)}")
            return {
                'symbol': symbol,
                'status': 'failed',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def fetch_and_store_multiple_stocks(self, symbols: List[str], include_history: bool = True,
                                      period: str = DEFAULT_PERIOD, interval: str = DEFAULT_INTERVAL,
                                      max_workers: int = MAX_CONCURRENT_REQUESTS,
                                      force_refresh: bool = False) -> Dict[str, Any]:
        """
        Fetch and store data for multiple stocks with concurrent processing
        
        Args:
            symbols: List of stock symbols
            include_history: Whether to include price history
            period: Period for historical data
            interval: Interval for historical data
            max_workers: Maximum number of concurrent workers
            force_refresh: Force refresh even if data is recent
            
        Returns:
            Dictionary containing batch processing results
        """
        self.logger.info(f"Starting batch processing for {len(symbols)} stocks")
        
        results = {
            'batch_timestamp': datetime.now().isoformat(),
            'total_symbols': len(symbols),
            'successful': 0,
            'failed': 0,
            'skipped': 0,
            'results': {}
        }
        
        # Filter symbols that need refresh if not forcing
        if not force_refresh:
            symbols_to_process = [s for s in symbols if self._needs_refresh(s)]
            skipped_count = len(symbols) - len(symbols_to_process)
            results['skipped'] = skipped_count
            self.logger.info(f"Skipping {skipped_count} stocks with recent data")
        else:
            symbols_to_process = symbols
        
        if not symbols_to_process:
            self.logger.info("No stocks need processing")
            return results
        
        # Process in batches with concurrent execution
        batch_size = min(BATCH_SIZE, len(symbols_to_process))
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_symbol = {}
            
            for i in range(0, len(symbols_to_process), batch_size):
                batch_symbols = symbols_to_process[i:i + batch_size]
                
                for symbol in batch_symbols:
                    future = executor.submit(
                        self.fetch_and_store_stock,
                        symbol,
                        include_history,
                        period,
                        interval,
                        force_refresh
                    )
                    future_to_symbol[future] = symbol
                
                # Add delay between batches to respect rate limits
                if i + batch_size < len(symbols_to_process):
                    time.sleep(2)
            
            # Collect results with progress bar
            with tqdm(total=len(symbols_to_process), desc="Processing stocks") as pbar:
                for future in as_completed(future_to_symbol):
                    symbol = future_to_symbol[future]
                    try:
                        result = future.result(timeout=REQUEST_TIMEOUT * 2)
                        results['results'][symbol] = result
                        
                        if result['status'] == 'success':
                            results['successful'] += 1
                        elif result['status'] == 'skipped':
                            results['skipped'] += 1
                        else:
                            results['failed'] += 1
                            
                    except Exception as e:
                        results['failed'] += 1
                        results['results'][symbol] = {
                            'symbol': symbol,
                            'status': 'failed',
                            'error': f"Execution error: {str(e)}",
                            'timestamp': datetime.now().isoformat()
                        }
                        self.logger.error(f"Execution error for {symbol}: {str(e)}")
                    
                    pbar.update(1)
        
        self.logger.info(f"Batch processing completed. Success: {results['successful']}, "
                        f"Failed: {results['failed']}, Skipped: {results['skipped']}")
        
        return results
    
    def fetch_sp500_data(self, include_history: bool = True, force_refresh: bool = False) -> Dict[str, Any]:
        """
        Fetch data for all S&P 500 stocks
        
        Args:
            include_history: Whether to include price history
            force_refresh: Force refresh even if data is recent
            
        Returns:
            Dictionary containing S&P 500 fetch results
        """
        self.logger.info("Starting S&P 500 data fetch")
        
        # Get S&P 500 symbols
        sp500_symbols = self.fetcher.get_sp500_symbols()
        
        if not sp500_symbols:
            return {
                'status': 'failed',
                'error': 'Could not retrieve S&P 500 symbols',
                'timestamp': datetime.now().isoformat()
            }
        
        # Fetch data for all S&P 500 stocks
        results = self.fetch_and_store_multiple_stocks(
            symbols=sp500_symbols,
            include_history=include_history,
            force_refresh=force_refresh
        )
        
        results['dataset'] = 'S&P 500'
        return results
    
    def _needs_refresh(self, symbol: str) -> bool:
        """
        Check if stock data needs to be refreshed based on last update time
        
        Args:
            symbol: Stock symbol
            
        Returns:
            True if data needs refresh, False otherwise
        """
        try:
            # Check last fetch time from database
            with sqlite3.connect(self.db_manager.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT MAX(fetch_timestamp) FROM fetch_log
                    WHERE symbol = ? AND success = 1
                ''', (symbol,))

                result = cursor.fetchone()
                if not result or not result[0]:
                    return True  # No previous successful fetch

                last_fetch = datetime.fromisoformat(result[0])
                hours_since_fetch = (datetime.now() - last_fetch).total_seconds() / 3600

                return hours_since_fetch >= DATA_REFRESH_HOURS

        except Exception as e:
            self.logger.debug(f"Error checking refresh need for {symbol}: {str(e)}")
            return True  # Default to refresh if we can't determine
    
    def get_stock_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve comprehensive stock data from database
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Dictionary containing all available stock data or None
        """
        try:
            # Get basic info
            info = self.db_manager.get_stock_info(symbol)
            if not info:
                return None
            
            # Get additional data
            price_history = self.db_manager.get_price_history(symbol)
            financials = self.db_manager.get_financials(symbol)
            earnings = self.db_manager.get_earnings(symbol)
            
            return {
                'symbol': symbol,
                'info': info,
                'price_history': price_history,
                'financials': financials,
                'earnings': earnings,
                'retrieved_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error retrieving data for {symbol}: {str(e)}")
            return None
    
    def search_stocks(self, query: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Search for stocks in the database
        
        Args:
            query: Search query (symbol, company name, sector, industry)
            limit: Maximum number of results
            
        Returns:
            List of matching stocks
        """
        return self.db_manager.search_stocks(query, limit)
    
    def get_database_stats(self) -> Dict[str, Any]:
        """
        Get database statistics
        
        Returns:
            Dictionary containing database statistics
        """
        return self.db_manager.get_database_stats()
